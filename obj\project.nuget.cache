{"version": 2, "dgSpecHash": "3Goy2qZymtQ=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\patchright-c-sharp\\Patchright.CSharp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.playwright\\1.40.0\\microsoft.playwright.1.40.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.0\\system.text.json.6.0.0.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "Error occurred while getting package vulnerability data: Unable to load the service index for source https://nuget.telerik.com/v3/index.json.", "projectPath": "C:\\Users\\<USER>\\source\\repos\\patchright-c-sharp\\Patchright.CSharp.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\source\\repos\\patchright-c-sharp\\Patchright.CSharp.csproj", "targetGraphs": []}]}