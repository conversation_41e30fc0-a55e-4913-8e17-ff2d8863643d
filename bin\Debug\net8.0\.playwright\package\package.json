{"name": "playwright-core", "version": "1.40.0", "description": "A high-level API to automate web browsers", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=16"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./package.json": "./package.json", "./lib/outofprocess": "./lib/outofprocess.js", "./lib/image_tools/stats": "./lib/image_tools/stats.js", "./lib/image_tools/compare": "./lib/image_tools/compare.js", "./lib/image_tools/imageChannel": "./lib/image_tools/imageChannel.js", "./lib/image_tools/colorUtils": "./lib/image_tools/colorUtils.js", "./lib/cli/cli": "./lib/cli/cli.js", "./lib/cli/program": "./lib/cli/program.js", "./lib/server/registry/index": "./lib/server/registry/index.js", "./lib/remote/playwrightServer": "./lib/remote/playwrightServer.js", "./lib/server": "./lib/server/index.js", "./lib/utils": "./lib/utils/index.js", "./lib/utilsBundle": "./lib/utilsBundle.js", "./lib/zipBundle": "./lib/zipBundle.js", "./types/protocol": "./types/protocol.d.ts", "./types/structs": "./types/structs.d.ts"}, "bin": {"playwright-core": "cli.js"}, "types": "types/types.d.ts"}