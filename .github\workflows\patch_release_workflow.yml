name: Trigger on Commit and Manual Request

on:
  push:
    branches:
      - "main"

permissions:
  actions: none
  attestations: none
  checks: none
  contents: write
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

jobs:
  run_action:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node v18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org'
      - name: Install TS-Morph
        run: npm install

      - name: Get Commit Author
        id: get_author
        run: |
          AUTHOR=$(git log -1 --pretty=format:'%an')
          echo "Commit author: $AUTHOR"
          echo "AUTHOR=$AUTHOR" >> $GITHUB_ENV

      - name: Get Original Commit Message
        id: get_commit_message
        run: |
          COMMIT_MESSAGE=$(git log -1 --pretty=format:'%s')
          echo "COMMIT_MESSAGE=$COMMIT_MESSAGE" >> $GITHUB_ENV

      - name: Skip if Commit is from GitHub Actions
        if: env.AUTHOR == 'github-actions'
        run: echo "Skipping action as the commit is from github-actions" && exit 0

      - name: Install Playwright Driver
        run: |
          response=$(curl --silent "https://api.github.com/repos/microsoft/playwright/releases/latest")
          version=$(echo "$response" | grep '"tag_name"' | sed -E 's/.*"([^"]+)".*/\1/')
          git clone https://github.com/microsoft/playwright --branch "$version"
          cd playwright
          npm ci

      - name: Copy Driver Directory to prepare for Patch-Comparison
        run: |
          cp -r playwright playwright_not_patched

      - name: Patch Playwright Driver
        run: |
          cd playwright
          node "../patchright_driver_patch.js"

      - name: Rename Patched Directory
        run: |
          mv playwright patchright
          mv playwright_not_patched playwright

      - name: Compare Directories and Create Patch File
        run: |
          echo "# NOTE: This patch file is generated automatically and is not used, it is only for documentation. The driver is actually patched using [patchright_driver_patch](https://github.com/Kaliiiiiiiiii-Vinyzu/patchright/blob/main/patchright_driver_patch.js), see [the workflow](https://github.com/Kaliiiiiiiiii-Vinyzu/patchright/blob/main/.github/workflows/patchright_workflow.yml)" > patchright.patch
          diff -ruN playwright patchright | sed -E 's/^(---|\+\+\+) .*/\1/' >> patchright.patch || true

      - name: Commit and Push Patch File
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "[Patch-Comparison] Automatic Commit: ${{ env.COMMIT_MESSAGE }}"
          branch: "main"
          file_pattern: "patchright.patch"
          skip_fetch: true
