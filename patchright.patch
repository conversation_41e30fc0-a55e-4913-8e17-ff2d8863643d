# NOTE: This patch file is generated automatically and is not used, it is only for documentation. The driver is actually patched using [patchright_driver_patch](https://github.com/Kaliiiiiiiiii-<PERSON>yzu/patchright/blob/main/patchright_driver_patch.js), see [the workflow](https://github.com/Kaliiiiiiiiii-Vinyzu/patchright/blob/main/.github/workflows/patchright_workflow.yml)
diff -ruN playwright/node_modules/playwright-core/src/server/bidi/bidiPage.ts patchright/node_modules/playwright-core/src/server/bidi/bidiPage.ts
---
+++
@@ -376,7 +376,7 @@
       this._initScriptIds.push(script);
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     const promises = this._initScriptIds.map(script => this._session.send('script.removePreloadScript', { script }));
     this._initScriptIds = [];
     await Promise.all(promises);
diff -ruN playwright/node_modules/playwright-core/src/server/browserContext.ts patchright/node_modules/playwright-core/src/server/browserContext.ts
---
+++
@@ -150,7 +150,7 @@
     if (debugMode() === 'console')
       await this.extendInjectedScript(consoleApiSource.source);
     if (this._options.serviceWorkers === 'block')
-      await this.addInitScript(`\nif (navigator.serviceWorker) navigator.serviceWorker.register = async () => { console.warn('Service Worker registration blocked by Playwright'); };\n`);
+      await this.addInitScript(`navigator.serviceWorker.register = async () => { };`);
 
     if (this._options.permissions)
       await this.grantPermissions(this._options.permissions);
@@ -333,16 +333,15 @@
     }
     const binding = new PageBinding(name, playwrightBinding, needsHandle);
     this._pageBindings.set(name, binding);
-    await this.doAddInitScript(binding.initScript);
-    const frames = this.pages().map(page => page.frames()).flat();
-    await Promise.all(frames.map(frame => frame.evaluateExpression(binding.initScript.source).catch(e => {})));
+    await this.doExposeBinding(binding);
   }
 
   async _removeExposedBindings() {
-    for (const [key, binding] of this._pageBindings) {
-      if (!binding.internal)
+    for (const key of this._pageBindings.keys()) {
+      if (!key.startsWith('__pw'))
         this._pageBindings.delete(key);
     }
+    await this.doRemoveExposedBindings();
   }
 
   async grantPermissions(permissions: string[], origin?: string) {
@@ -431,8 +430,8 @@
   }
 
   async _removeInitScripts(): Promise<void> {
-    this.initScripts = this.initScripts.filter(script => script.internal);
-    await this.doRemoveNonInternalInitScripts();
+    this.initScripts.splice(0, this.initScripts.length);
+    await this.doRemoveInitScripts();
   }
 
   async setRequestInterceptor(handler: network.RouteHandler | undefined): Promise<void> {
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/chromiumSwitches.ts patchright/node_modules/playwright-core/src/server/chromium/chromiumSwitches.ts
---
+++
@@ -58,27 +58,16 @@
   '--disable-field-trial-config', // https://source.chromium.org/chromium/chromium/src/+/main:testing/variations/README.md
   '--disable-background-networking',
   '--disable-background-timer-throttling',
-  '--disable-backgrounding-occluded-windows',
-  '--disable-back-forward-cache', // Avoids surprises like main request not being intercepted during page.goBack().
-  '--disable-breakpad',
-  '--disable-client-side-phishing-detection',
-  '--disable-component-extensions-with-background-pages',
-  '--disable-component-update', // Avoids unneeded network activity after startup.
+  '--disable-backgrounding-occluded-windows', // Avoids surprises like main request not being intercepted during page.goBack().
+  '--disable-breakpad', // Avoids unneeded network activity after startup.
   '--no-default-browser-check',
-  '--disable-default-apps',
   '--disable-dev-shm-usage',
-  '--disable-extensions',
   '--disable-features=' + disabledFeatures.join(','),
-  '--allow-pre-commit-input',
   '--disable-hang-monitor',
-  '--disable-ipc-flooding-protection',
-  '--disable-popup-blocking',
   '--disable-prompt-on-repost',
   '--disable-renderer-backgrounding',
   '--force-color-profile=srgb',
-  '--metrics-recording-only',
   '--no-first-run',
-  '--enable-automation',
   '--password-store=basic',
   '--use-mock-keychain',
   // See https://chromium-review.googlesource.com/c/chromium/src/+/2436773
@@ -86,6 +75,5 @@
   '--export-tagged-pdf',
   // https://chromium-review.googlesource.com/c/chromium/src/+/4853540
   '--disable-search-engine-choice-screen',
-  // https://issues.chromium.org/41491762
-  '--unsafely-disable-devtools-self-xss-warnings',
+  '--disable-blink-features=AutomationControlled'
 ];
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/crBrowser.ts patchright/node_modules/playwright-core/src/server/chromium/crBrowser.ts
---
+++
@@ -474,11 +474,6 @@
       await (page._delegate as CRPage).addInitScript(initScript);
   }
 
-  async doRemoveNonInternalInitScripts() {
-    for (const page of this.pages())
-      await (page._delegate as CRPage).removeNonInternalInitScripts();
-  }
-
   async doUpdateRequestInterception(): Promise<void> {
     for (const page of this.pages())
       await (page._delegate as CRPage).updateRequestInterception();
@@ -578,4 +573,16 @@
     const rootSession = await this._browser._clientRootSession();
     return rootSession.attachToTarget(targetId);
   }
+
+  async doRemoveInitScripts() {
+    for (const page of this.pages()) await (page._delegate as CRPage).removeInitScripts();
+  }
+
+  async doExposeBinding(binding: PageBinding) {
+    for (const page of this.pages()) await (page._delegate as CRPage).exposeBinding(binding);
+  }
+
+  async doRemoveExposedBindings() {
+    for (const page of this.pages()) await (page._delegate as CRPage).removeExposedBindings();
+  }
 }
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/crDevTools.ts patchright/node_modules/playwright-core/src/server/chromium/crDevTools.ts
---
+++
@@ -67,7 +67,6 @@
       }).catch(e => null);
     });
     Promise.all([
-      session.send('Runtime.enable'),
       session.send('Runtime.addBinding', { name: kBindingName }),
       session.send('Page.enable'),
       session.send('Page.addScriptToEvaluateOnNewDocument', { source: `
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/crNetworkManager.ts patchright/node_modules/playwright-core/src/server/chromium/crNetworkManager.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -156,7 +158,7 @@
     const enabled = this._protocolRequestInterceptionEnabled;
     if (initial && !enabled)
       return;
-    const cachePromise = info.session.send('Network.setCacheDisabled', { cacheDisabled: enabled });
+    const cachePromise = info.session.send('Network.setCacheDisabled', { cacheDisabled: false });
     let fetchPromise = Promise.resolve<any>(undefined);
     if (!info.workerFrame) {
       if (enabled)
@@ -238,6 +240,7 @@
   }
 
   _onRequestPaused(sessionInfo: SessionInfo, event: Protocol.Fetch.requestPausedPayload) {
+    if (this._alreadyTrackedNetworkIds.has(event.networkId)) return;
     if (!event.networkId) {
       // Fetch without networkId means that request was not recognized by inspector, and
       // it will never receive Network.requestWillBeSent. Continue the request to not affect it.
@@ -276,6 +279,7 @@
   }
 
   _onRequest(requestWillBeSentSessionInfo: SessionInfo, requestWillBeSentEvent: Protocol.Network.requestWillBeSentPayload, requestPausedSessionInfo: SessionInfo | undefined, requestPausedEvent: Protocol.Fetch.requestPausedPayload | undefined) {
+    if (this._alreadyTrackedNetworkIds.has(requestWillBeSentEvent.initiator.requestId)) return;
     if (requestWillBeSentEvent.request.url.startsWith('data:'))
       return;
     let redirectedFrom: InterceptableRequest | null = null;
@@ -342,7 +346,7 @@
         headersOverride = redirectedFrom?._originalRequestRoute?._alreadyContinuedParams?.headers;
         requestPausedSessionInfo!.session._sendMayFail('Fetch.continueRequest', { requestId: requestPausedEvent.requestId, headers: headersOverride });
       } else {
-        route = new RouteImpl(requestPausedSessionInfo!.session, requestPausedEvent.requestId);
+        route = new RouteImpl(requestPausedSessionInfo!.session, requestPausedEvent.requestId, this._page, requestPausedEvent.networkId, this);
       }
     }
     const isNavigationRequest = requestWillBeSentEvent.requestId === requestWillBeSentEvent.loaderId && requestWillBeSentEvent.type === 'Document';
@@ -547,6 +551,8 @@
     if (request.session !== sessionInfo.session && !sessionInfo.isMain && request._documentId === request._requestId)
       request.session = sessionInfo.session;
   }
+
+  _alreadyTrackedNetworkIds: Set<string> = new Set();
 }
 
 class InterceptableRequest {
@@ -606,32 +612,83 @@
   _alreadyContinuedParams: Protocol.Fetch.continueRequestParameters | undefined;
   _fulfilled: boolean = false;
 
-  constructor(session: CRSession, interceptionId: string) {
+  constructor(session: CRSession, interceptionId: string, page: Page, networkId, sessionManager) {
+    this._sessionManager = void 0;
+    this._networkId = void 0;
+    this._page = void 0;
     this._session = session;
     this._interceptionId = interceptionId;
+    this._page = page;
+    this._networkId = networkId;
+    this._sessionManager = sessionManager;
+    eventsHelper.addEventListener(this._session, 'Fetch.requestPaused', async e => await this._networkRequestIntercepted(e));
   }
 
   async continue(overrides: types.NormalizedContinueOverrides): Promise<void> {
     this._alreadyContinuedParams = {
-      requestId: this._interceptionId!,
+      requestId: this._interceptionId,
       url: overrides.url,
       headers: overrides.headers,
       method: overrides.method,
-      postData: overrides.postData ? overrides.postData.toString('base64') : undefined
+      postData: overrides.postData ? overrides.postData.toString('base64') : undefined,
     };
-    await catchDisallowedErrors(async () => {
-      await this._session.send('Fetch.continueRequest', this._alreadyContinuedParams);
-    });
+    if (overrides.url && (overrides.url === 'http://patchright-init-script-inject.internal/' || overrides.url === 'https://patchright-init-script-inject.internal/')) {
+      await catchDisallowedErrors(async () => {
+        this._sessionManager._alreadyTrackedNetworkIds.add(this._networkId);
+        this._session.send('Fetch.continueRequest', { requestId: this._interceptionId, interceptResponse: true });
+      }) ;
+    } else {
+      await catchDisallowedErrors(async () => {
+        await this._session.send('Fetch.continueRequest', this._alreadyContinuedParams);
+      });
+    }
   }
 
   async fulfill(response: types.NormalizedFulfillResponse) {
+    const isTextHtml = response.resourceType === 'Document' || response.headers.some(header => header.name === 'content-type' && header.value.includes('text/html'));
+    var allInjections = [...this._page._delegate._mainFrameSession._evaluateOnNewDocumentScripts];
+        for (const binding of this._page._delegate._browserContext._pageBindings.values()) {
+          if (!allInjections.includes(binding)) allInjections.push(binding);
+        }
+    if (isTextHtml && allInjections.length) {
+      // I Chatted so hard for this Code
+      let scriptNonce = crypto.randomBytes(22).toString('hex');
+      for (let i = 0; i < response.headers.length; i++) {
+        if (response.headers[i].name === 'content-security-policy' || response.headers[i].name === 'content-security-policy-report-only') {
+          // Search for an existing script-src nonce that we can hijack
+          let cspValue = response.headers[i].value;
+          const nonceRegex = /script-src[^;]*'nonce-([\w-]+)'/;
+          const nonceMatch = cspValue.match(nonceRegex);
+          if (nonceMatch) {
+            scriptNonce = nonceMatch[1];
+          } else {
+            // Add the new nonce value to the script-src directive
+            const scriptSrcRegex = /(script-src[^;]*)(;|$)/;
+            const newCspValue = cspValue.replace(scriptSrcRegex, `$1 'nonce-${scriptNonce}'$2`);
+            response.headers[i].value = newCspValue;
+          }
+          break;
+        }
+      }
+      let injectionHTML = "";
+      allInjections.forEach((script) => {
+        let scriptId = crypto.randomBytes(22).toString('hex');
+        let scriptSource = script.source || script;
+        injectionHTML += `<script class="${this._page._delegate.initScriptTag}" nonce="${scriptNonce}" type="text/javascript">document.getElementById("${scriptId}")?.remove();${scriptSource}</script>`;
+      });
+      if (response.isBase64) {
+        response.isBase64 = false;
+        response.body = injectionHTML + Buffer.from(response.body, 'base64').toString('utf-8');
+      } else {
+        response.body = injectionHTML + response.body;
+      }
+    }
     this._fulfilled = true;
     const body = response.isBase64 ? response.body : Buffer.from(response.body).toString('base64');
-
     const responseHeaders = splitSetCookieHeader(response.headers);
     await catchDisallowedErrors(async () => {
       await this._session.send('Fetch.fulfillRequest', {
-        requestId: this._interceptionId!,
+        requestId: response.interceptionId ? response.interceptionId : this._interceptionId,
         responseCode: response.status,
         responsePhrase: network.statusText(response.status),
         responseHeaders,
@@ -650,6 +707,33 @@
       });
     });
   }
+
+  async _networkRequestIntercepted(event) {
+    if (event.resourceType !== 'Document') {
+      /*await catchDisallowedErrors(async () => {
+        await this._session.send('Fetch.continueRequest', { requestId: event.requestId });
+      });*/
+      return;
+    }
+    if (this._networkId != event.networkId || !this._sessionManager._alreadyTrackedNetworkIds.has(event.networkId)) return;
+    try {
+      if (event.responseStatusCode >= 301 && event.responseStatusCode <= 308  || (event.redirectedRequestId && !event.responseStatusCode)) {
+        await this._session.send('Fetch.continueRequest', { requestId: event.requestId, interceptResponse: true });
+      } else {
+        const responseBody = await this._session.send('Fetch.getResponseBody', { requestId: event.requestId });
+        await this.fulfill({
+          headers: event.responseHeaders,
+          isBase64: true,
+          body: responseBody.body,
+          status: event.responseStatusCode,
+          interceptionId: event.requestId,
+          resourceType: event.resourceType,
+        })
+      }
+    } catch (error) {
+      await this._session._sendMayFail('Fetch.continueRequest', { requestId: event.requestId });
+    }
+  }
 }
 
 // In certain cases, protocol will return error if the request was already canceled
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/crPage.ts patchright/node_modules/playwright-core/src/server/chromium/crPage.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -101,7 +103,8 @@
     this.updateOffline();
     this.updateExtraHTTPHeaders();
     this.updateHttpCredentials();
-    this.updateRequestInterception();
+    this._networkManager.setRequestInterception(true);
+    this.initScriptTag = crypto.randomBytes(20).toString('hex');
     this._mainFrameSession = new FrameSession(this, client, targetId, null);
     this._sessions.set(targetId, this._mainFrameSession);
     if (opener && !browserContext._options.noDefaultViewport) {
@@ -231,10 +234,11 @@
   }
 
   async addInitScript(initScript: InitScript, world: types.World = 'main'): Promise<void> {
+    this._page.initScripts.push(initScript);
     await this._forAllFrameSessions(frame => frame._evaluateOnNewDocument(initScript, world));
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     await this._forAllFrameSessions(frame => frame._removeEvaluatesOnNewDocument());
   }
 
@@ -365,6 +369,15 @@
   shouldToggleStyleSheetToSyncAnimations(): boolean {
     return false;
   }
+
+  async exposeBinding(binding) {
+    await this._forAllFrameSessions(frame => frame._initBinding(binding));
+    await Promise.all(this._page.frames().map(frame => frame.evaluateExpression(binding.source).catch(e => {})));
+  }
+
+  async removeExposedBindings() {
+    await this._forAllFrameSessions(frame => frame._removeExposedBindings());
+  }
 }
 
 class FrameSession {
@@ -479,19 +492,6 @@
           this._handleFrameTree(frameTree);
           this._addRendererListeners();
         }
-
-        const localFrames = this._isMainFrame() ? this._page.frames() : [this._page._frameManager.frame(this._targetId)!];
-        for (const frame of localFrames) {
-          // Note: frames might be removed before we send these.
-          this._client._sendMayFail('Page.createIsolatedWorld', {
-            frameId: frame._id,
-            grantUniveralAccess: true,
-            worldName: UTILITY_WORLD_NAME,
-          });
-          for (const initScript of this._crPage._page.allInitScripts())
-            frame.evaluateExpression(initScript.source).catch(e => {});
-        }
-
         const isInitialEmptyPage = this._isMainFrame() && this._page.mainFrame().url() === ':';
         if (isInitialEmptyPage) {
           // Ignore lifecycle events, worlds and bindings for the initial empty page. It is never the final page
@@ -501,14 +501,20 @@
             this._eventListeners.push(eventsHelper.addEventListener(this._client, 'Page.lifecycleEvent', event => this._onLifecycleEvent(event)));
           });
         } else {
+          const localFrames = this._isMainFrame() ? this._page.frames() : [this._page._frameManager.frame(this._targetId)!];
+          for (const frame of localFrames) {
+            this._page._frameManager.frame(frame._id)._context("utility");
+            for (const binding of this._crPage._browserContext._pageBindings.values())
+              frame.evaluateExpression(binding.source).catch(e => {});
+            for (const source of this._crPage._browserContext.initScripts)
+              frame.evaluateExpression(source).catch(e => {});
+          }
           this._firstNonInitialNavigationCommittedFulfill();
           this._eventListeners.push(eventsHelper.addEventListener(this._client, 'Page.lifecycleEvent', event => this._onLifecycleEvent(event)));
         }
       }),
       this._client.send('Log.enable', {}),
       lifecycleEventsEnabled = this._client.send('Page.setLifecycleEventsEnabled', { enabled: true }),
-      this._client.send('Runtime.enable', {}),
-      this._client.send('Runtime.addBinding', { name: PageBinding.kPlaywrightBinding }),
       this._client.send('Page.addScriptToEvaluateOnNewDocument', {
         source: '',
         worldName: UTILITY_WORLD_NAME,
@@ -541,14 +547,16 @@
       promises.push(this._updateGeolocation(true));
       promises.push(this._updateEmulateMedia());
       promises.push(this._updateFileChooserInterception(true));
-      for (const initScript of this._crPage._page.allInitScripts())
-        promises.push(this._evaluateOnNewDocument(initScript, 'main'));
+      for (const binding of this._crPage._page.allBindings()) promises.push(this._initBinding(binding));
+      for (const initScript of this._crPage._browserContext.initScripts) promises.push(this._evaluateOnNewDocument(initScript, 'main'));
+      for (const initScript of this._crPage._page.initScripts) promises.push(this._evaluateOnNewDocument(initScript, 'main'));
       if (screencastOptions)
         promises.push(this._startVideoRecording(screencastOptions));
     }
-    promises.push(this._client.send('Runtime.runIfWaitingForDebugger'));
+    if (!(this._crPage._page._pageBindings.size || this._crPage._browserContext._pageBindings.size)) promises.push(this._client.send('Runtime.runIfWaitingForDebugger'));
     promises.push(this._firstNonInitialNavigationCommittedPromise);
     await Promise.all(promises);
+    if (this._crPage._page._pageBindings.size || this._crPage._browserContext._pageBindings.size) await this._client.send('Runtime.runIfWaitingForDebugger');
   }
 
   dispose() {
@@ -565,18 +573,31 @@
 
   async _navigate(frame: frames.Frame, url: string, referrer: string | undefined): Promise<frames.GotoResult> {
     const response = await this._client.send('Page.navigate', { url, referrer, frameId: frame._id, referrerPolicy: 'unsafeUrl' });
+    this._client._sendMayFail('Page.waitForDebugger');
     if (response.errorText)
       throw new frames.NavigationAbortedError(response.loaderId, `${response.errorText} at ${url}`);
     return { newDocumentId: response.loaderId };
   }
 
-  _onLifecycleEvent(event: Protocol.Page.lifecycleEventPayload) {
+  async _onLifecycleEvent(event: Protocol.Page.lifecycleEventPayload) {
     if (this._eventBelongsToStaleFrame(event.frameId))
       return;
     if (event.name === 'load')
       this._page._frameManager.frameLifecycleEvent(event.frameId, 'load');
     else if (event.name === 'DOMContentLoaded')
       this._page._frameManager.frameLifecycleEvent(event.frameId, 'domcontentloaded');
+    await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      var document = await this._client._sendMayFail("DOM.getDocument");
+      if (!document) return
+      var query = await this._client._sendMayFail("DOM.querySelectorAll", {
+        nodeId: document.root.nodeId,
+        selector: "[class=" + this._crPage.initScriptTag + "]"
+      });
+      if (!query) return
+      for (const nodeId of query.nodeIds) await this._client._sendMayFail("DOM.removeNode", { nodeId: nodeId });
+      await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      // ensuring execution context
+      try { await this._page._frameManager.frame(this._targetId)._context("utility") } catch { };
   }
 
   _handleFrameTree(frameTree: Protocol.Page.FrameTree) {
@@ -623,12 +644,24 @@
     this._page._frameManager.frameAttached(frameId, parentFrameId);
   }
 
-  _onFrameNavigated(framePayload: Protocol.Page.Frame, initial: boolean) {
+  async _onFrameNavigated(framePayload: Protocol.Page.Frame, initial: boolean) {
     if (this._eventBelongsToStaleFrame(framePayload.id))
       return;
     this._page._frameManager.frameCommittedNewDocumentNavigation(framePayload.id, framePayload.url + (framePayload.urlFragment || ''), framePayload.name || '', framePayload.loaderId, initial);
     if (!initial)
       this._firstNonInitialNavigationCommittedFulfill();
+    await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      var document = await this._client._sendMayFail("DOM.getDocument");
+      if (!document) return
+      var query = await this._client._sendMayFail("DOM.querySelectorAll", {
+        nodeId: document.root.nodeId,
+        selector: "[class=" + this._crPage.initScriptTag + "]"
+      });
+      if (!query) return
+      for (const nodeId of query.nodeIds) await this._client._sendMayFail("DOM.removeNode", { nodeId: nodeId });
+      await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      // ensuring execution context
+      try { await this._page._frameManager.frame(this._targetId)._context("utility") } catch { };
   }
 
   _onFrameRequestedNavigation(payload: Protocol.Page.frameRequestedNavigationPayload) {
@@ -665,19 +698,24 @@
   }
 
   _onExecutionContextCreated(contextPayload: Protocol.Runtime.ExecutionContextDescription) {
+    for (const name of this._exposedBindingNames) this._client._sendMayFail('Runtime.addBinding', { name: name, executionContextId: contextPayload.id });
     const frame = contextPayload.auxData ? this._page._frameManager.frame(contextPayload.auxData.frameId) : null;
+    if (contextPayload.auxData.type == "worker") throw new Error("ExecutionContext is worker");
     if (!frame || this._eventBelongsToStaleFrame(frame._id))
       return;
     const delegate = new CRExecutionContext(this._client, contextPayload);
-    let worldName: types.World|null = null;
-    if (contextPayload.auxData && !!contextPayload.auxData.isDefault)
-      worldName = 'main';
-    else if (contextPayload.name === UTILITY_WORLD_NAME)
-      worldName = 'utility';
+    let worldName = contextPayload.name;
     const context = new dom.FrameExecutionContext(delegate, frame, worldName);
     if (worldName)
       frame._contextCreated(worldName, context);
     this._contextIdToContext.set(contextPayload.id, context);
+    for (const source of this._exposedBindingScripts) {
+      this._client._sendMayFail("Runtime.evaluate", {
+        expression: source,
+        contextId: contextPayload.id,
+        awaitPromise: true,
+      })
+    }
   }
 
   _onExecutionContextDestroyed(executionContextId: number) {
@@ -693,7 +731,7 @@
       this._onExecutionContextDestroyed(contextId);
   }
 
-  _onAttachedToTarget(event: Protocol.Target.attachedToTargetPayload) {
+  async _onAttachedToTarget(event: Protocol.Target.attachedToTargetPayload) {
     const session = this._client.createChildSession(event.sessionId);
 
     if (event.targetInfo.type === 'iframe') {
@@ -725,8 +763,17 @@
     session.once('Runtime.executionContextCreated', async event => {
       worker._createExecutionContext(new CRExecutionContext(session, event.context));
     });
+    var globalThis = await session._sendMayFail('Runtime.evaluate', {
+      expression: "globalThis",
+      serializationOptions: { serialization: "idOnly" }
+
+    });
+    if (globalThis && globalThis.result) {
+      var globalThisObjId = globalThis.result.objectId;
+      var executionContextId = parseInt(globalThisObjId.split('.')[1], 10);
+      worker._createExecutionContext(new CRExecutionContext(session, { id: executionContextId }));
+    }
     // This might fail if the target is closed before we initialize.
-    session._sendMayFail('Runtime.enable');
     // TODO: attribute workers to the right frame.
     this._crPage._networkManager.addSession(session, this._page._frameManager.frame(this._targetId) ?? undefined).catch(() => {});
     session._sendMayFail('Runtime.runIfWaitingForDebugger');
@@ -805,8 +852,8 @@
     const pageOrError = await this._crPage._page.waitForInitializedOrError();
     if (!(pageOrError instanceof Error)) {
       const context = this._contextIdToContext.get(event.executionContextId);
-      if (context)
-        await this._page._onBindingCalled(event.payload, context);
+      if (context) await this._page._onBindingCalled(event.payload, context);
+      else await this._page._onBindingCalled(event.payload, (await this._page.mainFrame()._mainContext())) // This might be a bit sketchy but it works for now
     }
   }
 
@@ -1052,16 +1099,11 @@
   }
 
   async _evaluateOnNewDocument(initScript: InitScript, world: types.World): Promise<void> {
-    const worldName = world === 'utility' ? UTILITY_WORLD_NAME : undefined;
-    const { identifier } = await this._client.send('Page.addScriptToEvaluateOnNewDocument', { source: initScript.source, worldName });
-    if (!initScript.internal)
-      this._evaluateOnNewDocumentIdentifiers.push(identifier);
+    this._evaluateOnNewDocumentScripts.push(initScript)
   }
 
   async _removeEvaluatesOnNewDocument(): Promise<void> {
-    const identifiers = this._evaluateOnNewDocumentIdentifiers;
-    this._evaluateOnNewDocumentIdentifiers = [];
-    await Promise.all(identifiers.map(identifier => this._client.send('Page.removeScriptToEvaluateOnNewDocument', { identifier })));
+    this._evaluateOnNewDocumentScripts = [];
   }
 
   async _getContentFrame(handle: dom.ElementHandle): Promise<frames.Frame | null> {
@@ -1168,6 +1210,47 @@
       throw new Error(dom.kUnableToAdoptErrorMessage);
     return createHandle(to, result.object).asElement()!;
   }
+
+  _exposedBindingNames: string[] = [];
+  _evaluateOnNewDocumentScripts: string[] = [];
+  _parsedExecutionContextIds: number[] = [];
+  _exposedBindingScripts: string[] = [];
+
+  async _initBinding(binding = PageBinding) {
+    var result = await this._client._sendMayFail('Page.createIsolatedWorld', {
+      frameId: this._targetId, grantUniveralAccess: true, worldName: "utility"
+    });
+    if (!result) return
+    var isolatedContextId = result.executionContextId
+
+    var globalThis = await this._client._sendMayFail('Runtime.evaluate', {
+      expression: "globalThis",
+      serializationOptions: { serialization: "idOnly" }
+    });
+    if (!globalThis) return
+    var globalThisObjId = globalThis["result"]['objectId']
+    var mainContextId = parseInt(globalThisObjId.split('.')[1], 10);
+
+    await Promise.all([
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name }),
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name, executionContextId: mainContextId }),
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name, executionContextId: isolatedContextId }),
+      // this._client._sendMayFail("Runtime.evaluate", { expression: binding.source, contextId: mainContextId, awaitPromise: true })
+    ]);
+    this._exposedBindingNames.push(binding.name);
+    this._exposedBindingScripts.push(binding.source);
+    await this._crPage.addInitScript(binding.source);
+    //this._client._sendMayFail('Runtime.runIfWaitingForDebugger')
+  }
+
+  async _removeExposedBindings() {
+    const toRetain: string[] = [];
+    const toRemove: string[] = [];
+    for (const name of this._exposedBindingNames)
+      (name.startsWith('__pw_') ? toRetain : toRemove).push(name);
+    this._exposedBindingNames = toRetain;
+    await Promise.all(toRemove.map(name => this._client.send('Runtime.removeBinding', { name })));
+  }
 }
 
 async function emulateLocale(session: CRSession, locale: string) {
diff -ruN playwright/node_modules/playwright-core/src/server/chromium/crServiceWorker.ts patchright/node_modules/playwright-core/src/server/chromium/crServiceWorker.ts
---
+++
@@ -44,8 +44,6 @@
       this.updateOffline();
       this._networkManager.addSession(session, undefined, true /* isMain */).catch(() => {});
     }
-
-    session.send('Runtime.enable', {}).catch(e => { });
     session.send('Runtime.runIfWaitingForDebugger').catch(e => { });
     session.on('Inspector.targetReloadedAfterCrash', () => {
       // Resume service worker after restart.
diff -ruN playwright/node_modules/playwright-core/src/server/clock.ts patchright/node_modules/playwright-core/src/server/clock.ts
---
+++
@@ -92,6 +92,14 @@
   }
 
   private async _evaluateInFrames(script: string) {
+    // Dont ask me why this works
+    await Promise.all(this._browserContext.pages().map(async page => {
+      await Promise.all(page.frames().map(async frame => {
+        try {
+          await frame.evaluateExpression("");
+        } catch (e) {}
+      }));
+    }));
     await this._browserContext.safeNonStallingEvaluateInAllFrames(script, 'main', { throwOnJSErrors: true });
   }
 }
diff -ruN playwright/node_modules/playwright-core/src/server/dispatchers/frameDispatcher.ts patchright/node_modules/playwright-core/src/server/dispatchers/frameDispatcher.ts
---
+++
@@ -84,11 +84,15 @@
   }
 
   async evaluateExpression(params: channels.FrameEvaluateExpressionParams, metadata: CallMetadata): Promise<channels.FrameEvaluateExpressionResult> {
-    return { value: serializeResult(await this._frame.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._frame.evaluateExpression(params.expression, { isFunction: params.isFunction,
+      world: params.isolatedContext ? 'utility': 'main'
+    }, parseArgument(params.arg))) };
   }
 
   async evaluateExpressionHandle(params: channels.FrameEvaluateExpressionHandleParams, metadata: CallMetadata): Promise<channels.FrameEvaluateExpressionHandleResult> {
-    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._frame.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._frame.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction,
+      world: params.isolatedContext ? 'utility': 'main'
+    }, parseArgument(params.arg))) };
   }
 
   async waitForSelector(params: channels.FrameWaitForSelectorParams, metadata: CallMetadata): Promise<channels.FrameWaitForSelectorResult> {
diff -ruN playwright/node_modules/playwright-core/src/server/dispatchers/jsHandleDispatcher.ts patchright/node_modules/playwright-core/src/server/dispatchers/jsHandleDispatcher.ts
---
+++
@@ -39,11 +39,11 @@
   }
 
   async evaluateExpression(params: channels.JSHandleEvaluateExpressionParams): Promise<channels.JSHandleEvaluateExpressionResult> {
-    return { value: serializeResult(await this._object.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._object.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg), params.isolatedContext)) };
   }
 
   async evaluateExpressionHandle(params: channels.JSHandleEvaluateExpressionHandleParams): Promise<channels.JSHandleEvaluateExpressionHandleResult> {
-    const jsHandle = await this._object.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg));
+    const jsHandle = await this._object.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg), params.isolatedContext);
     return { handle: ElementHandleDispatcher.fromJSHandle(this.parentScope(), jsHandle) };
   }
 
diff -ruN playwright/node_modules/playwright-core/src/server/dispatchers/pageDispatcher.ts patchright/node_modules/playwright-core/src/server/dispatchers/pageDispatcher.ts
---
+++
@@ -353,11 +353,11 @@
   }
 
   async evaluateExpression(params: channels.WorkerEvaluateExpressionParams, metadata: CallMetadata): Promise<channels.WorkerEvaluateExpressionResult> {
-    return { value: serializeResult(await this._object.evaluateExpression(params.expression, params.isFunction, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._object.evaluateExpression(params.expression, params.isFunction, parseArgument(params.arg), params.isolatedContext)) };
   }
 
   async evaluateExpressionHandle(params: channels.WorkerEvaluateExpressionHandleParams, metadata: CallMetadata): Promise<channels.WorkerEvaluateExpressionHandleResult> {
-    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._object.evaluateExpressionHandle(params.expression, params.isFunction, parseArgument(params.arg))) };
+    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._object.evaluateExpressionHandle(params.expression, params.isFunction, parseArgument(params.arg), params.isolatedContext)) };
   }
 }
 
diff -ruN playwright/node_modules/playwright-core/src/server/firefox/ffPage.ts patchright/node_modules/playwright-core/src/server/firefox/ffPage.ts
---
+++
@@ -390,7 +390,7 @@
     await this._session.send('Page.setInitScripts', { scripts: this._initScripts.map(s => ({ script: s.initScript.source, worldName: s.worldName })) });
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     this._initScripts = this._initScripts.filter(s => s.initScript.internal);
     await this._session.send('Page.setInitScripts', { scripts: this._initScripts.map(s => ({ script: s.initScript.source, worldName: s.worldName })) });
   }
diff -ruN playwright/node_modules/playwright-core/src/server/frameSelectors.ts patchright/node_modules/playwright-core/src/server/frameSelectors.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import { ElementHandle } from './dom';
 /**
  * Copyright (c) Microsoft Corporation.
  *
@@ -134,9 +136,29 @@
           throw injected.createStacklessError(`Selector "${selectorString}" resolved to ${injected.previewNode(element)}, <iframe> was expected`);
         return element;
       }, { info, scope: i === 0 ? scope : undefined, selectorString: stringifySelector(info.parsed) });
-      const element = handle.asElement() as ElementHandle<Element> | null;
-      if (!element)
-        return null;
+      let element = handle.asElement() as ElementHandle<Element> | null;
+      if (!element) {
+        try {
+          var client = frame._page._delegate._sessionForFrame(frame)._client;
+        } catch (e) {
+          var client = frame._page._delegate._mainFrameSession._client;
+        }
+        var mainContext = await frame._context("main");
+        const documentNode = await client.send("Runtime.evaluate", {
+          expression: "document",
+          serializationOptions: {
+            serialization: "idOnly"
+          },
+          contextId: mainContext.delegate._contextId
+        });
+        const documentScope = new ElementHandle(mainContext, documentNode.result.objectId);
+        var check = await this._customFindFramesByParsed(injectedScript, client, mainContext, documentScope, info.parsed);
+        if (check.length > 0) {
+          element = check[0];
+        } else {
+          return null;
+        }
+      }
       const maybeFrame = await frame._page._delegate.getContentFrame(element);
       element.dispose();
       if (!maybeFrame)
@@ -158,6 +180,123 @@
     const injected = await context.injectedScript();
     return { injected, info: resolved.info, frame: resolved.frame, scope: resolved.scope };
   }
+
+  async _customFindFramesByParsed(injected, client, context, documentScope, parsed) {
+    var parsedEdits = { ...parsed };
+    var currentScopingElements = [documentScope];
+    while (parsed.parts.length > 0) {
+      var part = parsed.parts.shift();
+      parsedEdits.parts = [part];
+      var elements = [];
+      var elementsIndexes = [];
+      if (part.name == "nth") {
+        const partNth = Number(part.body);
+        if (partNth > currentScopingElements.length || partNth < -currentScopingElements.length) {
+          return continuePolling;
+        } else {
+          currentScopingElements = [currentScopingElements.at(partNth)];
+          continue;
+        }
+      } else if (part.name == "internal:or") {
+        var orredElements = await this._customFindFramesByParsed(injected, client, context, documentScope, part.body.parsed);
+        elements = currentScopingElements.concat(orredElements);
+      } else if (part.name == "internal:and") {
+        var andedElements = await this._customFindFramesByParsed(injected, client, context, documentScope, part.body.parsed);
+        const backendNodeIds = new Set(andedElements.map((item) => item.backendNodeId));
+        elements = currentScopingElements.filter((item) => backendNodeIds.has(item.backendNodeId));
+      } else {
+        for (const scope of currentScopingElements) {
+          const describedScope = await client.send("DOM.describeNode", {
+            objectId: scope._objectId,
+            depth: -1,
+            pierce: true
+          });
+          var queryingElements = [];
+          let findClosedShadowRoots2 = function(node, results = []) {
+            if (!node || typeof node !== "object") return results;
+            if (node.shadowRoots && Array.isArray(node.shadowRoots)) {
+              for (const shadowRoot2 of node.shadowRoots) {
+                if (shadowRoot2.shadowRootType === "closed" && shadowRoot2.backendNodeId) {
+                  results.push(shadowRoot2.backendNodeId);
+                }
+                findClosedShadowRoots2(shadowRoot2, results);
+              }
+            }
+            if (node.nodeName !== "IFRAME" && node.children && Array.isArray(node.children)) {
+              for (const child of node.children) {
+                findClosedShadowRoots2(child, results);
+              }
+            }
+            return results;
+          };
+          var findClosedShadowRoots = findClosedShadowRoots2;
+          var shadowRootBackendIds = findClosedShadowRoots2(describedScope.node);
+          var shadowRoots = [];
+          for (var shadowRootBackendId of shadowRootBackendIds) {
+            var resolvedShadowRoot = await client.send("DOM.resolveNode", {
+              backendNodeId: shadowRootBackendId,
+              contextId: context.delegate._contextId
+            });
+            shadowRoots.push(new ElementHandle(context, resolvedShadowRoot.object.objectId));
+          }
+          for (var shadowRoot of shadowRoots) {
+            const shadowElements = await shadowRoot.evaluateHandleInUtility(([injected, node, { parsed: parsed2 }]) => {
+              const elements2 = injected.querySelectorAll(parsed2, node);
+              return elements2;
+            }, {
+              parsed: parsedEdits,
+            });
+            const shadowElementsAmount = await shadowElements.getProperty("length");
+            queryingElements.push([shadowElements, shadowElementsAmount, shadowRoot]);
+          }
+          const rootElements = await scope.evaluateHandleInUtility(([injected, node, { parsed: parsed2 }]) => {
+            const elements2 = injected.querySelectorAll(parsed2, node);
+            return elements2;
+          }, {
+            parsed: parsedEdits
+          });
+          const rootElementsAmount = await rootElements.getProperty("length");
+          queryingElements.push([rootElements, rootElementsAmount, injected]);
+          for (var queryedElement of queryingElements) {
+            var elementsToCheck = queryedElement[0];
+            var elementsAmount = await queryedElement[1].jsonValue();
+            var parentNode = queryedElement[2];
+            for (var i = 0; i < elementsAmount; i++) {
+              if (parentNode.constructor.name == "ElementHandle") {
+                var elementToCheck = await parentNode.evaluateHandleInUtility(([injected, node, { index, elementsToCheck: elementsToCheck2 }]) => {
+                  return elementsToCheck2[index];
+                }, { index: i, elementsToCheck });
+              } else {
+                var elementToCheck = await parentNode.evaluateHandle((injected, { index, elementsToCheck: elementsToCheck2 }) => {
+                  return elementsToCheck2[index];
+                }, { index: i, elementsToCheck });
+              }
+              elementToCheck.parentNode = parentNode;
+              var resolvedElement = await client.send("DOM.describeNode", {
+                objectId: elementToCheck._objectId,
+                depth: -1
+              });
+              elementToCheck.backendNodeId = resolvedElement.node.backendNodeId;
+              elements.push(elementToCheck);
+            }
+          }
+        }
+      }
+      currentScopingElements = [];
+      for (var element of elements) {
+        var elemIndex = element.backendNodeId;
+        var elemPos = elementsIndexes.findIndex((index) => index > elemIndex);
+        if (elemPos === -1) {
+          currentScopingElements.push(element);
+          elementsIndexes.push(elemIndex);
+        } else {
+          currentScopingElements.splice(elemPos, 0, element);
+          elementsIndexes.splice(elemPos, 0, elemIndex);
+        }
+      }
+    }
+    return currentScopingElements;
+  }
 }
 
 async function adoptIfNeeded<T extends Node>(handle: ElementHandle<T>, context: FrameExecutionContext): Promise<ElementHandle<T>> {
diff -ruN playwright/node_modules/playwright-core/src/server/frames.ts patchright/node_modules/playwright-core/src/server/frames.ts
---
+++
@@ -1,3 +1,7 @@
+// patchright - custom imports
+import { CRExecutionContext } from './chromium/crExecutionContext';
+import { FrameExecutionContext } from './dom';
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -530,6 +534,9 @@
   }
 
   _onClearLifecycle() {
+    this._isolatedWorld = undefined;
+    this._mainWorld = undefined;
+    this._iframeWorld = undefined;
     for (const event of this._firedLifecycleEvents)
       this.emit(Frame.Events.RemoveLifecycle, event);
     this._firedLifecycleEvents.clear();
@@ -743,12 +750,68 @@
     return this._page._delegate.getFrameElement(this);
   }
 
-  _context(world: types.World): Promise<dom.FrameExecutionContext> {
-    return this._contextData.get(world)!.contextPromise.then(contextOrDestroyedReason => {
-      if (contextOrDestroyedReason instanceof js.ExecutionContext)
-        return contextOrDestroyedReason;
-      throw new Error(contextOrDestroyedReason.destroyedReason);
-    });
+  async _context(world: types.World): Promise<dom.FrameExecutionContext> {
+    /* await this._page._delegate._mainFrameSession._client._sendMayFail('DOM.enable');
+        var globalDoc = await this._page._delegate._mainFrameSession._client._sendMayFail('DOM.getFrameOwner', { frameId: this._id });
+        if (globalDoc) {
+          await this._page._delegate._mainFrameSession._client._sendMayFail("DOM.resolveNode", { nodeId: globalDoc.nodeId })
+        } */
+
+        // if (this.isDetached()) throw new Error('Frame was detached');
+        try {
+          var client = this._page._delegate._sessionForFrame(this)._client
+        } catch (e) { var client = this._page._delegate._mainFrameSession._client }
+        var iframeExecutionContextId = await this._getFrameMainFrameContextId(client)
+
+        if (world == "main") {
+          // Iframe Only
+          if (this != this._page.mainFrame() && iframeExecutionContextId && this._iframeWorld == undefined) {
+            var executionContextId = iframeExecutionContextId
+            var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+            this._iframeWorld = new FrameExecutionContext(crContext, this, world)
+            this._page._delegate._mainFrameSession._onExecutionContextCreated({
+              id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+            })
+          } else if (this._mainWorld == undefined) {
+            var globalThis = await client._sendMayFail('Runtime.evaluate', {
+              expression: "globalThis",
+              serializationOptions: { serialization: "idOnly" }
+            });
+            if (!globalThis) { return }
+            var globalThisObjId = globalThis["result"]['objectId']
+            var executionContextId = parseInt(globalThisObjId.split('.')[1], 10);
+
+            var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+            this._mainWorld = new FrameExecutionContext(crContext, this, world)
+            this._page._delegate._mainFrameSession._onExecutionContextCreated({
+              id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+            })
+          }
+        }
+        if (world != "main" && this._isolatedWorld == undefined) {
+          world = "utility"
+          var result = await client._sendMayFail('Page.createIsolatedWorld', {
+            frameId: this._id, grantUniveralAccess: true, worldName: world
+          });
+          if (!result) {
+            // if (this.isDetached()) throw new Error("Frame was detached");
+            return
+          }
+          var executionContextId = result.executionContextId
+          var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+          this._isolatedWorld = new FrameExecutionContext(crContext, this, world)
+          this._page._delegate._mainFrameSession._onExecutionContextCreated({
+            id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+          })
+        }
+
+        if (world != "main") {
+          return this._isolatedWorld;
+        } else if (this != this._page.mainFrame() && iframeExecutionContextId) {
+          return this._iframeWorld;
+        } else {
+          return this._mainWorld;
+        }
   }
 
   _mainContext(): Promise<dom.FrameExecutionContext> {
@@ -796,58 +859,49 @@
   }
 
   async waitForSelectorInternal(progress: Progress, selector: string, performActionPreChecks: boolean, options: types.WaitForElementOptions, scope?: dom.ElementHandle): Promise<dom.ElementHandle<Element> | null> {
-    const { state = 'visible' } = options;
-    const promise = this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-      if (performActionPreChecks)
-        await this._page.performActionPreChecks(progress);
-
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-      progress.throwIfAborted();
-      if (!resolved) {
-        if (state === 'hidden' || state === 'detached')
-          return null;
-        return continuePolling;
-      }
-      const result = await resolved.injected.evaluateHandle((injected, { info, root }) => {
-        if (root && !root.isConnected)
-          throw injected.createStacklessError('Element is not attached to the DOM');
-        const elements = injected.querySelectorAll(info.parsed, root || document);
-        const element: Element | undefined  = elements[0];
-        const visible = element ? injected.utils.isElementVisible(element) : false;
-        let log = '';
-        if (elements.length > 1) {
-          if (info.strict)
-            throw injected.strictModeViolationError(info.parsed, elements);
-          log = `  locator resolved to ${elements.length} elements. Proceeding with the first one: ${injected.previewNode(elements[0])}`;
-        } else if (element) {
-          log = `  locator resolved to ${visible ? 'visible' : 'hidden'} ${injected.previewNode(element)}`;
+    const {
+      state = 'visible'
+    } = options;
+    const promise = this._retryWithProgressIfNotConnected(progress, selector, options.strict, true, async handle => {
+      const attached = !!handle;
+      var visible = false;
+      if (attached) {
+        if (handle.parentNode.constructor.name == "ElementHandle") {
+          visible = await handle.parentNode.evaluateInUtility(([injected, node, { handle }]) => {
+            return handle ? injected.utils.isElementVisible(handle) : false;
+          }, {
+            handle
+          });
+        } else {
+          visible = await handle.parentNode.evaluate((injected, { handle }) => {
+            return handle ? injected.utils.isElementVisible(handle) : false;
+          }, {
+            handle
+          });
         }
-        return { log, element, visible, attached: !!element };
-      }, { info: resolved.info, root: resolved.frame === this ? scope : undefined });
-      const { log, visible, attached } = await result.evaluate(r => ({ log: r.log, visible: r.visible, attached: r.attached }));
-      if (log)
-        progress.log(log);
-      const success = { attached, detached: !attached, visible, hidden: !visible }[state];
+      }
+
+      const success = {
+        attached,
+        detached: !attached,
+        visible,
+        hidden: !visible
+      }[state];
       if (!success) {
-        result.dispose();
-        return continuePolling;
+        return "internal:continuepolling";
       }
       if (options.omitReturnValue) {
-        result.dispose();
         return null;
       }
-      const element = state === 'attached' || state === 'visible' ? await result.evaluateHandle(r => r.element) : null;
-      result.dispose();
-      if (!element)
-        return null;
-      if ((options as any).__testHookBeforeAdoptNode)
-        await (options as any).__testHookBeforeAdoptNode();
+      const element = state === 'attached' || state === 'visible' ? handle : null;
+      if (!element) return null;
+      if (options.__testHookBeforeAdoptNode) await options.__testHookBeforeAdoptNode();
       try {
-        return await element._adoptTo(await resolved.frame._mainContext());
+        return element;
       } catch (e) {
-        return continuePolling;
+        return "internal:continuepolling";
       }
-    });
+    }, "returnOnNotResolved");
     return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
   }
 
@@ -886,7 +940,22 @@
   }
 
   async queryCount(selector: string): Promise<number> {
-    return await this.selectors.queryCount(selector);
+    const custom_metadata = {
+      "internal": false,
+      "log": []
+    };
+    const controller = new ProgressController(custom_metadata, this);
+    const resultPromise = await controller.run(async progress => {
+      progress.log("waiting for " + this._asLocator(selector));
+      const promise = await this._retryWithProgressIfNotConnected(progress, selector, false, false, async result => {
+        if (!result) return 0;
+        const handle = result[0];
+        const handles = result[1];
+        return handle ? handles.length : 0;
+      }, 'returnAll');
+      return promise;
+    }, 10000); // A bit geeky but its okay :D
+    return resultPromise ? resultPromise : 0;
   }
 
   async content(): Promise<string> {
@@ -908,31 +977,38 @@
   }
 
   async setContent(metadata: CallMetadata, html: string, options: types.NavigateOptions = {}): Promise<void> {
-    const controller = new ProgressController(metadata, this);
-    return controller.run(async progress => {
-      await this.raceNavigationAction(progress, options, async () => {
-        const waitUntil = options.waitUntil === undefined ? 'load' : options.waitUntil;
-        progress.log(`setting frame content, waiting until "${waitUntil}"`);
-        const tag = `--playwright--set--content--${this._id}--${++this._setContentCounter}--`;
-        const context = await this._utilityContext();
-        const lifecyclePromise = new Promise((resolve, reject) => {
-          this._page._frameManager._consoleMessageTags.set(tag, () => {
-            // Clear lifecycle right after document.open() - see 'tag' below.
-            this._onClearLifecycle();
-            this._waitForLoadState(progress, waitUntil).then(resolve).catch(reject);
+      const controller = new ProgressController(metadata, this);
+      return controller.run(async progress => {
+        await this.raceNavigationAction(progress, options, async () => {
+          const waitUntil = options.waitUntil === undefined ? 'load' : options.waitUntil;
+          progress.log(`setting frame content, waiting until "${waitUntil}"`);
+          const tag = `--playwright--set--content--${this._id}--${++this._setContentCounter}--`;
+          const bindingName = "_tagDebug" + crypto.randomBytes(20).toString('hex');
+          const context = await this._utilityContext();
+          await this._page._delegate._mainFrameSession._client.send('Runtime.addBinding', { name: bindingName });
+          const lifecyclePromise = new Promise(async (resolve, reject) => {
+            await this._page.exposeBinding(bindingName, false, (tag) => {
+              this._onClearLifecycle();
+              this._waitForLoadState(progress, waitUntil).then(resolve).catch(reject);
+            });
+          });
+          const contentPromise = context.evaluate(({ html, tag, bindingName }) => {
+            document.open();
+            var _tagDebug = window[bindingName].bind({});
+            delete window[bindingName]
+            _tagDebug('{ "name": "' + bindingName + '", "seq": 1, "serializedArgs": ["' + tag + '"] }');
+            console.debug(tag);  // eslint-disable-line no-console
+            document.write(html);
+            document.close();
+          }, { html, tag,
+            bindingName
           });
+          await Promise.all([contentPromise, lifecyclePromise]);
+          return null;
         });
-        const contentPromise = context.evaluate(({ html, tag }) => {
-          document.open();
-          console.debug(tag);  // eslint-disable-line no-console
-          document.write(html);
-          document.close();
-        }, { html, tag });
-        await Promise.all([contentPromise, lifecyclePromise]);
-        return null;
-      });
-    }, this._page._timeoutSettings.navigationTimeout(options));
-  }
+      }, this._page._timeoutSettings.navigationTimeout(options));
+    }
+
 
   name(): string {
     return this._name || '';
@@ -1125,50 +1201,80 @@
     selector: string,
     strict: boolean | undefined,
     performActionPreChecks: boolean,
-    action: (handle: dom.ElementHandle<Element>) => Promise<R | 'error:notconnected'>): Promise<R> {
-    progress.log(`waiting for ${this._asLocator(selector)}`);
+    action: (handle: dom.ElementHandle<Element>) => Promise<R | 'error:notconnected'>, returnAction: boolean | undefined): Promise<R> {
+    progress.log("waiting for " + this._asLocator(selector));
     return this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-      if (performActionPreChecks)
-        await this._page.performActionPreChecks(progress);
-
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, { strict });
+      if (performActionPreChecks) await this._page.performActionPreChecks(progress);
+      const resolved = await this.selectors.resolveInjectedForSelector(selector, {
+        strict
+      });
       progress.throwIfAborted();
-      if (!resolved)
+      if (!resolved) {
+        if (returnAction === 'returnOnNotResolved' || returnAction === 'returnAll') {
+        const result = await action(null);
+        return result === "internal:continuepolling" ? continuePolling2 : result;
+      }
+        return continuePolling;
+      }
+
+      try {
+        var client = this._page._delegate._sessionForFrame(resolved.frame)._client;
+      } catch (e) {
+        var client = this._page._delegate._mainFrameSession._client;
+      }
+      var context = await resolved.frame._context("main");
+
+      const documentNode = await client.send('Runtime.evaluate', {
+        expression: "document",
+        serializationOptions: {
+          serialization: "idOnly"
+        },
+        contextId: context.delegate._contextId,
+      });
+      const documentScope = new dom.ElementHandle(context, documentNode.result.objectId);
+
+      const currentScopingElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, resolved.info.parsed);
+      if (currentScopingElements.length == 0) {
+        // TODO: Dispose?
+        if (returnAction === 'returnOnNotResolved' || returnAction === 'returnAll') {
+        const result = await action(null);
+        return result === "internal:continuepolling" ? continuePolling2 : result;
+      }
         return continuePolling;
-      const result = await resolved.injected.evaluateHandle((injected, { info, callId }) => {
-        const elements = injected.querySelectorAll(info.parsed, document);
-        if (callId)
-          injected.markTargetElements(new Set(elements), callId);
-        const element = elements[0] as Element | undefined;
-        let log = '';
-        if (elements.length > 1) {
-          if (info.strict)
+      }
+      const resultElement = currentScopingElements[0];
+      if (currentScopingElements.length > 1) {
+        if (resolved.info.strict) {
+          await resolved.injected.evaluateHandle((injected, {
+            info,
+            elements
+          }) => {
             throw injected.strictModeViolationError(info.parsed, elements);
-          log = `  locator resolved to ${elements.length} elements. Proceeding with the first one: ${injected.previewNode(elements[0])}`;
-        } else if (element) {
-          log = `  locator resolved to ${injected.previewNode(element)}`;
+          }, {
+            info: resolved.info,
+            elements: currentScopingElements
+          });
         }
-        return { log, success: !!element, element };
-      }, { info: resolved.info, callId: progress.metadata.id });
-      const { log, success } = await result.evaluate(r => ({ log: r.log, success: r.success }));
-      if (log)
-        progress.log(log);
-      if (!success) {
-        result.dispose();
-        return continuePolling;
+        progress.log("  locator resolved to " + currentScopingElements.length + " elements. Proceeding with the first one: " + resultElement.preview());
+      } else if (resultElement) {
+        progress.log("  locator resolved to " + resultElement.preview());
       }
-      const element = await result.evaluateHandle(r => r.element) as dom.ElementHandle<Element>;
-      result.dispose();
+
       try {
-        const result = await action(element);
+        var result = null;
+        if (returnAction === 'returnAll') {
+          result = await action([resultElement, currentScopingElements]);
+        } else {
+          result = await action(resultElement);
+        }
         if (result === 'error:notconnected') {
           progress.log('element was detached from the DOM, retrying');
           return continuePolling;
+        } else if (result === 'internal:continuepolling') {
+          return continuePolling;
         }
         return result;
-      } finally {
-        element?.dispose();
-      }
+      } finally { }
     });
   }
 
@@ -1319,17 +1425,35 @@
 
   async isVisibleInternal(selector: string, options: types.StrictOptions = {}, scope?: dom.ElementHandle): Promise<boolean> {
     try {
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-      if (!resolved)
-        return false;
-      return await resolved.injected.evaluate((injected, { info, root }) => {
-        const element = injected.querySelector(info.parsed, root || document, info.strict);
-        const state = element ? injected.elementState(element, 'visible') : { matches: false, received: 'error:notconnected' };
-        return state.matches;
-      }, { info: resolved.info, root: resolved.frame === this ? scope : undefined });
+      const custom_metadata = { "internal": false, "log": [] };
+      const controller = new ProgressController(custom_metadata, this);
+      return await controller.run(async progress => {
+        progress.log("waiting for " + this._asLocator(selector));
+        const promise = this._retryWithProgressIfNotConnected(progress, selector, options.strict, false, async handle => {
+          if (!handle) return false;
+          if (handle.parentNode.constructor.name == "ElementHandle") {
+            return await handle.parentNode.evaluateInUtility(([injected, node, { handle }]) => {
+              const state = handle ? injected.elementState(handle, 'visible') : {
+                matches: false,
+                received: 'error:notconnected'
+              };
+              return state.matches;
+            }, { handle });
+          } else {
+            return await handle.parentNode.evaluate((injected, { handle }) => {
+              const state = handle ? injected.elementState(handle, 'visible') : {
+                matches: false,
+                received: 'error:notconnected'
+              };
+              return state.matches;
+            }, { handle });
+          }
+        }, "returnOnNotResolved");
+
+        return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
+      }, 10000) || false; // A bit geeky but its okay :D
     } catch (e) {
-      if (js.isJavaScriptErrorInEvaluate(e) || isInvalidSelectorError(e) || isSessionClosedError(e))
-        throw e;
+      if (js.isJavaScriptErrorInEvaluate(e) || isInvalidSelectorError(e) || isSessionClosedError(e)) throw e;
       return false;
     }
   }
@@ -1489,40 +1613,46 @@
   }
 
   private async _expectInternal(progress: Progress, selector: string, options: FrameExpectParams, lastIntermediateResult: { received?: any, isSet: boolean }) {
-    const selectorInFrame = await this.selectors.resolveFrameForSelector(selector, { strict: true });
-    progress.throwIfAborted();
+    progress.log("waiting for " + this._asLocator(selector));
+    const isArray = options.expression === 'to.have.count' || options.expression.endsWith('.array');
 
-    const { frame, info } = selectorInFrame || { frame: this, info: undefined };
-    const world = options.expression === 'to.have.property' ? 'main' : (info?.world ?? 'utility');
-    const context = await frame._context(world);
-    const injected = await context.injectedScript();
-    progress.throwIfAborted();
+    const promise = await this._retryWithProgressIfNotConnected(progress, selector, !isArray, false, async result => {
+      const handle = result[0];
+      const handles = result[1];
+
+      if (handle.parentNode.constructor.name == "ElementHandle") {
+        return await handle.parentNode.evaluateInUtility(async ([injected, node, { handle, options, handles }]) => {
+          return await injected.expect(handle, options, handles);
+        }, { handle, options, handles });
+      } else {
+        return await handle.parentNode.evaluate(async (injected, { handle, options, handles }) => {
+          return await injected.expect(handle, options, handles);
+        }, { handle, options, handles });
+      }
+    }, 'returnAll');
 
-    const { log, matches, received, missingReceived } = await injected.evaluate(async (injected, { info, options, callId }) => {
-      const elements = info ? injected.querySelectorAll(info.parsed, document) : [];
-      if (callId)
-        injected.markTargetElements(new Set(elements), callId);
-      const isArray = options.expression === 'to.have.count' || options.expression.endsWith('.array');
-      let log = '';
-      if (isArray)
-        log = `  locator resolved to ${elements.length} element${elements.length === 1 ? '' : 's'}`;
-      else if (elements.length > 1)
-        throw injected.strictModeViolationError(info!.parsed, elements);
-      else if (elements.length)
-        log = `  locator resolved to ${injected.previewNode(elements[0])}`;
-      return { log, ...await injected.expect(elements[0], options, elements) };
-    }, { info, options, callId: progress.metadata.id });
-
-    if (log)
-      progress.log(log);
-    // Note: missingReceived avoids `unexpected value "undefined"` when element was not found.
+    // Default Values, if no Elements found
+    var matches = false;
+    var received = 0;
+    var missingReceived = null;
+    if (promise) {
+      matches = promise.matches;
+      received = promise.received;
+      missingReceived = promise.missingReceived;
+    } else if (options.expectedNumber === 0) {
+      matches = true;
+    }
+
+    // Note: missingReceived avoids unexpected value "undefined" when element was not found.
     if (matches === options.isNot) {
       lastIntermediateResult.received = missingReceived ? '<element(s) not found>' : received;
       lastIntermediateResult.isSet = true;
-      if (!missingReceived && !Array.isArray(received))
-        progress.log(`  unexpected value "${renderUnexpectedValue(options.expression, received)}"`);
+      if (!missingReceived && !Array.isArray(received)) progress.log('  unexpected value "' + renderUnexpectedValue(options.expression, received) + '"');
     }
-    return { matches, received };
+    return {
+      matches,
+      received
+    };
   }
 
   async _waitForFunctionExpression<R>(metadata: CallMetadata, expression: string, isFunction: boolean | undefined, arg: any, options: types.WaitForFunctionOptions, world: types.World = 'main'): Promise<js.SmartHandle<R>> {
@@ -1632,28 +1762,27 @@
     const callbackText = body.toString();
     const controller = new ProgressController(metadata, this);
     return controller.run(async progress => {
-      progress.log(`waiting for ${this._asLocator(selector)}`);
-      const promise = this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-        const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-        progress.throwIfAborted();
-        if (!resolved)
-          return continuePolling;
-        const { log, success, value } = await resolved.injected.evaluate((injected, { info, callbackText, taskData, callId, root }) => {
-          const callback = injected.eval(callbackText) as ElementCallback<T, R>;
-          const element = injected.querySelector(info.parsed, root || document, info.strict);
-          if (!element)
-            return { success: false };
-          const log = `  locator resolved to ${injected.previewNode(element)}`;
-          if (callId)
-            injected.markTargetElements(new Set([element]), callId);
-          return { log, success: true, value: callback(injected, element, taskData as T) };
-        }, { info: resolved.info, callbackText, taskData, callId: progress.metadata.id, root: resolved.frame === this ? scope : undefined });
-
-        if (log)
-          progress.log(log);
-        if (!success)
-          return continuePolling;
-        return value!;
+      progress.log("waiting for "+ this._asLocator(selector));
+      const promise = this._retryWithProgressIfNotConnected(progress, selector, false, false, async handle => {
+        if (handle.parentNode.constructor.name == "ElementHandle") {
+          return await handle.parentNode.evaluateInUtility(([injected, node, { callbackText, handle, taskData }]) => {
+            const callback = injected.eval(callbackText);
+            return callback(injected, handle, taskData);
+          }, {
+            callbackText,
+            handle,
+            taskData
+          });
+        } else {
+          return await handle.parentNode.evaluate((injected, { callbackText, handle, taskData }) => {
+            const callback = injected.eval(callbackText);
+            return callback(injected, handle, taskData);
+          }, {
+            callbackText,
+            handle,
+            taskData
+          });
+        }
       });
       return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
     }, this._page._timeoutSettings.timeout(options));
@@ -1757,6 +1886,163 @@
   private _asLocator(selector: string) {
     return asLocator(this._page.attribution.playwright.options.sdkLanguage, selector);
   }
+
+  _isolatedWorld: dom.FrameExecutionContext;
+  _mainWorld: dom.FrameExecutionContext;
+  _iframeWorld: dom.FrameExecutionContext;
+
+  async _getFrameMainFrameContextId(client): Promise<number> {
+    try {
+        var globalDocument = await client._sendMayFail("DOM.getFrameOwner", {frameId: this._id,});
+        if (globalDocument && globalDocument.nodeId) {
+          var describedNode = await client._sendMayFail("DOM.describeNode", {
+            backendNodeId: globalDocument.backendNodeId,
+          });
+          if (describedNode) {
+            var resolvedNode = await client._sendMayFail("DOM.resolveNode", {
+              nodeId: describedNode.node.contentDocument.nodeId,
+            });
+            var _executionContextId = parseInt(resolvedNode.object.objectId.split(".")[1], 10);
+            return _executionContextId;
+            }
+          }
+        } catch (e) {}
+        return 0;
+  }
+
+  async _customFindElementsByParsed(resolved, client, context, documentScope, progress, parsed) {
+    var parsedEdits = { ...parsed };
+    // Note: We start scoping at document level
+    var currentScopingElements = [documentScope];
+    while (parsed.parts.length > 0) {
+      var part = parsed.parts.shift();
+      parsedEdits.parts = [part];
+      // Getting All Elements
+      var elements = [];
+      var elementsIndexes = [];
+
+      if (part.name == "nth") {
+        const partNth = Number(part.body);
+        if (partNth > currentScopingElements.length || partNth < -currentScopingElements.length) {
+          return continuePolling;
+        } else {
+          currentScopingElements = [currentScopingElements.at(partNth)];
+          continue;
+        }
+      } else if (part.name == "internal:or") {
+        var orredElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, part.body.parsed);
+        elements = currentScopingElements.concat(orredElements);
+      } else if (part.name == "internal:and") {
+        var andedElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, part.body.parsed);
+        const backendNodeIds = new Set(andedElements.map(item => item.backendNodeId));
+        elements = currentScopingElements.filter(item => backendNodeIds.has(item.backendNodeId));
+      } else {
+        for (const scope of currentScopingElements) {
+          const describedScope = await client.send('DOM.describeNode', {
+            objectId: scope._objectId,
+            depth: -1,
+            pierce: true
+          });
+
+          // Elements Queryed in the "current round"
+          var queryingElements = [];
+          function findClosedShadowRoots(node, results = []) {
+            if (!node || typeof node !== 'object') return results;
+            if (node.shadowRoots && Array.isArray(node.shadowRoots)) {
+              for (const shadowRoot of node.shadowRoots) {
+                if (shadowRoot.shadowRootType === 'closed' && shadowRoot.backendNodeId) {
+                  results.push(shadowRoot.backendNodeId);
+                }
+                findClosedShadowRoots(shadowRoot, results);
+              }
+            }
+            if (node.nodeName !== 'IFRAME' && node.children && Array.isArray(node.children)) {
+              for (const child of node.children) {
+                findClosedShadowRoots(child, results);
+              }
+            }
+            return results;
+          }
+
+          var shadowRootBackendIds = findClosedShadowRoots(describedScope.node);
+          var shadowRoots = [];
+          for (var shadowRootBackendId of shadowRootBackendIds) {
+            var resolvedShadowRoot = await client.send('DOM.resolveNode', {
+              backendNodeId: shadowRootBackendId,
+              contextId: context.delegate._contextId
+            });
+            shadowRoots.push(new dom.ElementHandle(context, resolvedShadowRoot.object.objectId));
+          }
+
+          for (var shadowRoot of shadowRoots) {
+            const shadowElements = await shadowRoot.evaluateHandleInUtility(([injected, node, { parsed, callId }]) => {
+             const elements = injected.querySelectorAll(parsed, node);
+              if (callId) injected.markTargetElements(new Set(elements), callId);
+              return elements
+            }, {
+              parsed: parsedEdits,
+              callId: progress.metadata.id
+            });
+
+            const shadowElementsAmount = await shadowElements.getProperty("length");
+            queryingElements.push([shadowElements, shadowElementsAmount, shadowRoot]);
+          }
+
+          // Document Root Elements (not in CSR)
+          const rootElements = await scope.evaluateHandleInUtility(([injected, node, { parsed, callId }]) => {
+            const elements = injected.querySelectorAll(parsed, node);
+            if (callId) injected.markTargetElements(new Set(elements), callId);
+            return elements
+          }, {
+            parsed: parsedEdits,
+            callId: progress.metadata.id
+          });
+          const rootElementsAmount = await rootElements.getProperty("length");
+          queryingElements.push([rootElements, rootElementsAmount, resolved.injected]);
+
+          // Querying and Sorting the elements by their backendNodeId
+          for (var queryedElement of queryingElements) {
+            var elementsToCheck = queryedElement[0];
+            var elementsAmount = await queryedElement[1].jsonValue();
+            var parentNode = queryedElement[2];
+            for (var i = 0; i < elementsAmount; i++) {
+              if (parentNode.constructor.name == "ElementHandle") {
+                var elementToCheck = await parentNode.evaluateHandleInUtility(([injected, node, { index, elementsToCheck }]) => { return elementsToCheck[index]; }, { index: i, elementsToCheck: elementsToCheck });
+              } else {
+                var elementToCheck = await parentNode.evaluateHandle((injected, { index, elementsToCheck }) => { return elementsToCheck[index]; }, { index: i, elementsToCheck: elementsToCheck });
+              }
+              // For other Functions/Utilities
+              elementToCheck.parentNode = parentNode;
+              var resolvedElement = await client.send('DOM.describeNode', {
+                objectId: elementToCheck._objectId,
+                depth: -1,
+              });
+              // Note: Possible Bug, Maybe well actually have to check the Documents Node Position instead of using the backendNodeId
+              elementToCheck.backendNodeId = resolvedElement.node.backendNodeId;
+              elements.push(elementToCheck);
+            }
+          }
+        }
+      }
+      // Setting currentScopingElements to the elements we just queried
+      currentScopingElements = [];
+      for (var element of elements) {
+        var elemIndex = element.backendNodeId;
+        // Sorting the Elements by their occourance in the DOM
+        var elemPos = elementsIndexes.findIndex(index => index > elemIndex);
+
+        // Sort the elements by their backendNodeId
+        if (elemPos === -1) {
+          currentScopingElements.push(element);
+          elementsIndexes.push(elemIndex);
+        } else {
+          currentScopingElements.splice(elemPos, 0, element);
+          elementsIndexes.splice(elemPos, 0, elemIndex);
+        }
+      }
+    }
+    return currentScopingElements;
+  }
 }
 
 class SignalBarrier {
diff -ruN playwright/node_modules/playwright-core/src/server/javascript.ts patchright/node_modules/playwright-core/src/server/javascript.ts
---
+++
@@ -154,17 +154,33 @@
     return evaluate(this._context, false /* returnByValue */, pageFunction, this, arg);
   }
 
-  async evaluateExpression(expression: string, options: { isFunction?: boolean }, arg: any) {
-    const value = await evaluateExpression(this._context, expression, { ...options, returnByValue: true }, this, arg);
-    await this._context.doSlowMo();
-    return value;
-  }
+  async evaluateExpression(expression: string, options: { isFunction?: boolean }, arg: any, isolatedContext?: boolean) {
+    let context = this._context;
+      if (context.constructor.name === "FrameExecutionContext") {
+          const frame = context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      const value = await evaluateExpression(context, expression, { ...options, returnByValue: true }, this, arg);
+      await context.doSlowMo();
+      return value;
+    }
 
-  async evaluateExpressionHandle(expression: string, options: { isFunction?: boolean }, arg: any): Promise<JSHandle<any>> {
-    const value = await evaluateExpression(this._context, expression, { ...options, returnByValue: false }, this, arg);
-    await this._context.doSlowMo();
-    return value;
-  }
+  async evaluateExpressionHandle(expression: string, options: { isFunction?: boolean }, arg: any, isolatedContext?: boolean): Promise<JSHandle<any>> {
+    let context = this._context;
+      if (this._context.constructor.name === "FrameExecutionContext") {
+          const frame = this._context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      const value = await evaluateExpression(context, expression, { ...options, returnByValue: false }, this, arg);
+      await context.doSlowMo();
+      return value;
+    }
 
   async getProperty(propertyName: string): Promise<JSHandle> {
     const objectHandle = await this.evaluateHandle((object: any, propertyName) => {
diff -ruN playwright/node_modules/playwright-core/src/server/page.ts patchright/node_modules/playwright-core/src/server/page.ts
---
+++
@@ -61,7 +61,7 @@
   goForward(): Promise<boolean>;
   requestGC(): Promise<void>;
   addInitScript(initScript: InitScript): Promise<void>;
-  removeNonInternalInitScripts(): Promise<void>;
+  removeInitScripts(): Promise<void>;
   closePage(runBeforeUnload: boolean): Promise<void>;
 
   navigateFrame(frame: frames.Frame, url: string, referrer: string | undefined): Promise<frames.GotoResult>;
@@ -352,15 +352,15 @@
       throw new Error(`Function "${name}" has been already registered in the browser context`);
     const binding = new PageBinding(name, playwrightBinding, needsHandle);
     this._pageBindings.set(name, binding);
-    await this._delegate.addInitScript(binding.initScript);
-    await Promise.all(this.frames().map(frame => frame.evaluateExpression(binding.initScript.source).catch(e => {})));
+    await this._delegate.exposeBinding(binding);
   }
 
   async _removeExposedBindings() {
-    for (const [key, binding] of this._pageBindings) {
-      if (!binding.internal)
+    for (const key of this._pageBindings.keys()) {
+      if (!key.startsWith('__pw'))
         this._pageBindings.delete(key);
     }
+    await this._delegate.removeExposedBindings();
   }
 
   setExtraHTTPHeaders(headers: types.HeadersArray) {
@@ -577,8 +577,8 @@
   }
 
   async _removeInitScripts() {
-    this.initScripts = this.initScripts.filter(script => script.internal);
-    await this._delegate.removeNonInternalInitScripts();
+    this.initScripts.splice(0, this.initScripts.length);
+    await this._delegate.removeInitScripts();
   }
 
   needsRequestInterception(): boolean {
@@ -770,11 +770,6 @@
       this._browserContext.addVisitedOrigin(origin);
   }
 
-  allInitScripts() {
-    const bindings = [...this._browserContext._pageBindings.values(), ...this._pageBindings.values()];
-    return [kBuiltinsScript, ...bindings.map(binding => binding.initScript), ...this._browserContext.initScripts, ...this.initScripts];
-  }
-
   getBinding(name: string) {
     return this._pageBindings.get(name) || this._browserContext._pageBindings.get(name);
   }
@@ -811,6 +806,10 @@
   markAsServerSideOnly() {
     this._isServerSideOnly = true;
   }
+
+  allBindings() {
+    return [...this._browserContext._pageBindings.values(), ...this._pageBindings.values()];
+  }
 }
 
 export class Worker extends SdkObject {
@@ -848,30 +847,42 @@
     this.openScope.close(new Error('Worker closed'));
   }
 
-  async evaluateExpression(expression: string, isFunction: boolean | undefined, arg: any): Promise<any> {
-    return js.evaluateExpression(await this._executionContextPromise, expression, { returnByValue: true, isFunction }, arg);
-  }
+  async evaluateExpression(expression: string, isFunction: boolean | undefined, arg: any, isolatedContext?: boolean): Promise<any> {
+    let context = await this._executionContextPromise;
+      if (context.constructor.name === "FrameExecutionContext") {
+          const frame = context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      return js.evaluateExpression(context, expression, { returnByValue: true, isFunction }, arg);
+    }
 
-  async evaluateExpressionHandle(expression: string, isFunction: boolean | undefined, arg: any): Promise<any> {
-    return js.evaluateExpression(await this._executionContextPromise, expression, { returnByValue: false, isFunction }, arg);
-  }
+  async evaluateExpressionHandle(expression: string, isFunction: boolean | undefined, arg: any, isolatedContext?: boolean): Promise<any> {
+    let context = await this._executionContextPromise;
+      if (this._context.constructor.name === "FrameExecutionContext") {
+          const frame = this._context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      return js.evaluateExpression(context, expression, { returnByValue: false, isFunction }, arg);
+    }
 }
 
 export class PageBinding {
-  static kPlaywrightBinding = '__playwright__binding__';
-
   readonly name: string;
   readonly playwrightFunction: frames.FunctionWithSource;
-  readonly initScript: InitScript;
   readonly needsHandle: boolean;
   readonly internal: boolean;
 
   constructor(name: string, playwrightFunction: frames.FunctionWithSource, needsHandle: boolean) {
     this.name = name;
     this.playwrightFunction = playwrightFunction;
-    this.initScript = new InitScript(createPageBindingScript(PageBinding.kPlaywrightBinding, name, needsHandle), true /* internal */);
+    this.source = createPageBindingScript(name, needsHandle);
     this.needsHandle = needsHandle;
-    this.internal = name.startsWith('__pw');
   }
 
   static async dispatch(page: Page, payload: string, context: dom.FrameExecutionContext) {
@@ -896,6 +907,8 @@
       context.evaluate(deliverBindingResult, { name, seq, error }).catch(e => debugLogger.log('error', e));
     }
   }
+
+  readonly source: string;
 }
 
 export class InitScript {
@@ -905,14 +918,7 @@
 
   constructor(source: string, internal?: boolean, name?: string) {
     const guid = createGuid();
-    this.source = `(() => {
-      globalThis.__pwInitScripts = globalThis.__pwInitScripts || {};
-      const hasInitScript = globalThis.__pwInitScripts[${JSON.stringify(guid)}];
-      if (hasInitScript)
-        return;
-      globalThis.__pwInitScripts[${JSON.stringify(guid)}] = true;
-      ${source}
-    })();`;
+    this.source = `(() => { ${source} })();`;
     this.internal = !!internal;
     this.name = name;
   }
diff -ruN playwright/node_modules/playwright-core/src/server/pageBinding.ts patchright/node_modules/playwright-core/src/server/pageBinding.ts
---
+++
@@ -26,10 +26,11 @@
   serializedArgs?: SerializedValue[],
 };
 
-function addPageBinding(playwrightBinding: string, bindingName: string, needsHandle: boolean, utilityScriptSerializersFactory: typeof source, builtins: Builtins) {
+function addPageBinding(bindingName: string, needsHandle: boolean, utilityScriptSerializersFactory: typeof source, builtins: Builtins) {
   const { serializeAsCallArgument } = utilityScriptSerializersFactory(builtins);
   // eslint-disable-next-line no-restricted-globals
-  const binding = (globalThis as any)[playwrightBinding];
+  const binding = (globalThis as any)[bindingName];
+  if (!binding || binding.toString().startsWith("(...args) => {")) return
   // eslint-disable-next-line no-restricted-globals
   (globalThis as any)[bindingName] = (...args: any[]) => {
   // eslint-disable-next-line no-restricted-globals
@@ -66,7 +67,6 @@
     return promise;
   };
   // eslint-disable-next-line no-restricted-globals
-  (globalThis as any)[bindingName].__installed = true;
 }
 
 export function takeBindingHandle(arg: { name: string, seq: number }) {
@@ -87,6 +87,6 @@
   callbacks.delete(arg.seq);
 }
 
-export function createPageBindingScript(playwrightBinding: string, name: string, needsHandle: boolean) {
-  return `(${addPageBinding.toString()})(${JSON.stringify(playwrightBinding)}, ${JSON.stringify(name)}, ${needsHandle}, (${source}), (${builtins})())`;
+export function createPageBindingScript(name: string, needsHandle: boolean) {
+  return `(${addPageBinding.toString()})(${JSON.stringify(name)}, ${needsHandle}, (${source}), (${builtins})())`;
 }
diff -ruN playwright/node_modules/playwright-core/src/server/webkit/wkPage.ts patchright/node_modules/playwright-core/src/server/webkit/wkPage.ts
---
+++
@@ -768,7 +768,7 @@
     await this._updateBootstrapScript();
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     await this._updateBootstrapScript();
   }
 
diff -ruN playwright/node_modules/playwright-core/src/utils/isomorphic/builtins.ts patchright/node_modules/playwright-core/src/utils/isomorphic/builtins.ts
---
+++
@@ -43,8 +43,7 @@
 // builtins instead of initializing them again.
 export function builtins(global?: typeof globalThis): Builtins {
   global = global ?? globalThis;
-  if (!(global as any)['__playwright_builtins__']) {
-    const builtins: Builtins = {
+    return {
       setTimeout: global.setTimeout?.bind(global),
       clearTimeout: global.clearTimeout?.bind(global),
       setInterval: global.setInterval?.bind(global),
@@ -58,11 +57,8 @@
       Intl: global.Intl,
       Date: global.Date,
       Map: global.Map,
-      Set: global.Set,
+      Set: global.Set
     };
-    Object.defineProperty(global, '__playwright_builtins__', { value: builtins, configurable: false, enumerable: false, writable: false });
-  }
-  return (global as any)['__playwright_builtins__'];
 }
 
 const instance = builtins();
diff -ruN playwright/packages/injected/src/xpathSelectorEngine.ts patchright/packages/injected/src/xpathSelectorEngine.ts
---
+++
@@ -18,6 +18,47 @@
 
 export const XPathEngine: SelectorEngine = {
   queryAll(root: SelectorRoot, selector: string): Element[] {
+    if (root.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {
+      const result: Element[] = [];
+      // Custom ClosedShadowRoot XPath Engine
+      const parser = new DOMParser();
+      // Function to (recursively) get all elements in the shadowRoot
+      function getAllChildElements(node) {
+        const elements = [];
+        const traverse = (currentNode) => {
+          if (currentNode.nodeType === Node.ELEMENT_NODE) elements.push(currentNode);
+          currentNode.childNodes?.forEach(traverse);
+        };
+        if (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE) {
+          traverse(node);
+        }
+
+        return elements;
+      }
+
+      // Setting innerHTMl and childElements (all, recursive) to avoid race conditions
+      const csrHTMLContent = root.innerHTML;
+      const csrChildElements = getAllChildElements(root);
+      const htmlDoc = parser.parseFromString(csrHTMLContent, 'text/html');
+      const rootDiv = htmlDoc.body
+      const rootDivChildElements = getAllChildElements(rootDiv);
+      // Use the namespace prefix in the XPath expression
+      const it = htmlDoc.evaluate(selector, htmlDoc, null, XPathResult.ORDERED_NODE_ITERATOR_TYPE);
+      for (let node = it.iterateNext(); node; node = it.iterateNext()) {
+        // -1 for the body element
+        const nodeIndex = rootDivChildElements.indexOf(node) - 1;
+        if (nodeIndex >= 0) {
+          const originalNode = csrChildElements[nodeIndex];
+          if (originalNode.nodeType === Node.ELEMENT_NODE)
+            result.push(originalNode as Element);
+        }
+
+      }
+
+      return result;
+    }
+
+    
     if (selector.startsWith('/') && root.nodeType !== Node.DOCUMENT_NODE)
       selector = '.' + selector;
     const result: Element[] = [];
diff -ruN playwright/packages/playwright-core/src/server/bidi/bidiPage.ts patchright/packages/playwright-core/src/server/bidi/bidiPage.ts
---
+++
@@ -376,7 +376,7 @@
       this._initScriptIds.push(script);
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     const promises = this._initScriptIds.map(script => this._session.send('script.removePreloadScript', { script }));
     this._initScriptIds = [];
     await Promise.all(promises);
diff -ruN playwright/packages/playwright-core/src/server/browserContext.ts patchright/packages/playwright-core/src/server/browserContext.ts
---
+++
@@ -150,7 +150,7 @@
     if (debugMode() === 'console')
       await this.extendInjectedScript(consoleApiSource.source);
     if (this._options.serviceWorkers === 'block')
-      await this.addInitScript(`\nif (navigator.serviceWorker) navigator.serviceWorker.register = async () => { console.warn('Service Worker registration blocked by Playwright'); };\n`);
+      await this.addInitScript(`navigator.serviceWorker.register = async () => { };`);
 
     if (this._options.permissions)
       await this.grantPermissions(this._options.permissions);
@@ -333,16 +333,15 @@
     }
     const binding = new PageBinding(name, playwrightBinding, needsHandle);
     this._pageBindings.set(name, binding);
-    await this.doAddInitScript(binding.initScript);
-    const frames = this.pages().map(page => page.frames()).flat();
-    await Promise.all(frames.map(frame => frame.evaluateExpression(binding.initScript.source).catch(e => {})));
+    await this.doExposeBinding(binding);
   }
 
   async _removeExposedBindings() {
-    for (const [key, binding] of this._pageBindings) {
-      if (!binding.internal)
+    for (const key of this._pageBindings.keys()) {
+      if (!key.startsWith('__pw'))
         this._pageBindings.delete(key);
     }
+    await this.doRemoveExposedBindings();
   }
 
   async grantPermissions(permissions: string[], origin?: string) {
@@ -431,8 +430,8 @@
   }
 
   async _removeInitScripts(): Promise<void> {
-    this.initScripts = this.initScripts.filter(script => script.internal);
-    await this.doRemoveNonInternalInitScripts();
+    this.initScripts.splice(0, this.initScripts.length);
+    await this.doRemoveInitScripts();
   }
 
   async setRequestInterceptor(handler: network.RouteHandler | undefined): Promise<void> {
diff -ruN playwright/packages/playwright-core/src/server/chromium/chromiumSwitches.ts patchright/packages/playwright-core/src/server/chromium/chromiumSwitches.ts
---
+++
@@ -58,27 +58,16 @@
   '--disable-field-trial-config', // https://source.chromium.org/chromium/chromium/src/+/main:testing/variations/README.md
   '--disable-background-networking',
   '--disable-background-timer-throttling',
-  '--disable-backgrounding-occluded-windows',
-  '--disable-back-forward-cache', // Avoids surprises like main request not being intercepted during page.goBack().
-  '--disable-breakpad',
-  '--disable-client-side-phishing-detection',
-  '--disable-component-extensions-with-background-pages',
-  '--disable-component-update', // Avoids unneeded network activity after startup.
+  '--disable-backgrounding-occluded-windows', // Avoids surprises like main request not being intercepted during page.goBack().
+  '--disable-breakpad', // Avoids unneeded network activity after startup.
   '--no-default-browser-check',
-  '--disable-default-apps',
   '--disable-dev-shm-usage',
-  '--disable-extensions',
   '--disable-features=' + disabledFeatures.join(','),
-  '--allow-pre-commit-input',
   '--disable-hang-monitor',
-  '--disable-ipc-flooding-protection',
-  '--disable-popup-blocking',
   '--disable-prompt-on-repost',
   '--disable-renderer-backgrounding',
   '--force-color-profile=srgb',
-  '--metrics-recording-only',
   '--no-first-run',
-  '--enable-automation',
   '--password-store=basic',
   '--use-mock-keychain',
   // See https://chromium-review.googlesource.com/c/chromium/src/+/2436773
@@ -86,6 +75,5 @@
   '--export-tagged-pdf',
   // https://chromium-review.googlesource.com/c/chromium/src/+/4853540
   '--disable-search-engine-choice-screen',
-  // https://issues.chromium.org/41491762
-  '--unsafely-disable-devtools-self-xss-warnings',
+  '--disable-blink-features=AutomationControlled'
 ];
diff -ruN playwright/packages/playwright-core/src/server/chromium/crBrowser.ts patchright/packages/playwright-core/src/server/chromium/crBrowser.ts
---
+++
@@ -474,11 +474,6 @@
       await (page._delegate as CRPage).addInitScript(initScript);
   }
 
-  async doRemoveNonInternalInitScripts() {
-    for (const page of this.pages())
-      await (page._delegate as CRPage).removeNonInternalInitScripts();
-  }
-
   async doUpdateRequestInterception(): Promise<void> {
     for (const page of this.pages())
       await (page._delegate as CRPage).updateRequestInterception();
@@ -578,4 +573,16 @@
     const rootSession = await this._browser._clientRootSession();
     return rootSession.attachToTarget(targetId);
   }
+
+  async doRemoveInitScripts() {
+    for (const page of this.pages()) await (page._delegate as CRPage).removeInitScripts();
+  }
+
+  async doExposeBinding(binding: PageBinding) {
+    for (const page of this.pages()) await (page._delegate as CRPage).exposeBinding(binding);
+  }
+
+  async doRemoveExposedBindings() {
+    for (const page of this.pages()) await (page._delegate as CRPage).removeExposedBindings();
+  }
 }
diff -ruN playwright/packages/playwright-core/src/server/chromium/crDevTools.ts patchright/packages/playwright-core/src/server/chromium/crDevTools.ts
---
+++
@@ -67,7 +67,6 @@
       }).catch(e => null);
     });
     Promise.all([
-      session.send('Runtime.enable'),
       session.send('Runtime.addBinding', { name: kBindingName }),
       session.send('Page.enable'),
       session.send('Page.addScriptToEvaluateOnNewDocument', { source: `
diff -ruN playwright/packages/playwright-core/src/server/chromium/crNetworkManager.ts patchright/packages/playwright-core/src/server/chromium/crNetworkManager.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -156,7 +158,7 @@
     const enabled = this._protocolRequestInterceptionEnabled;
     if (initial && !enabled)
       return;
-    const cachePromise = info.session.send('Network.setCacheDisabled', { cacheDisabled: enabled });
+    const cachePromise = info.session.send('Network.setCacheDisabled', { cacheDisabled: false });
     let fetchPromise = Promise.resolve<any>(undefined);
     if (!info.workerFrame) {
       if (enabled)
@@ -238,6 +240,7 @@
   }
 
   _onRequestPaused(sessionInfo: SessionInfo, event: Protocol.Fetch.requestPausedPayload) {
+    if (this._alreadyTrackedNetworkIds.has(event.networkId)) return;
     if (!event.networkId) {
       // Fetch without networkId means that request was not recognized by inspector, and
       // it will never receive Network.requestWillBeSent. Continue the request to not affect it.
@@ -276,6 +279,7 @@
   }
 
   _onRequest(requestWillBeSentSessionInfo: SessionInfo, requestWillBeSentEvent: Protocol.Network.requestWillBeSentPayload, requestPausedSessionInfo: SessionInfo | undefined, requestPausedEvent: Protocol.Fetch.requestPausedPayload | undefined) {
+    if (this._alreadyTrackedNetworkIds.has(requestWillBeSentEvent.initiator.requestId)) return;
     if (requestWillBeSentEvent.request.url.startsWith('data:'))
       return;
     let redirectedFrom: InterceptableRequest | null = null;
@@ -342,7 +346,7 @@
         headersOverride = redirectedFrom?._originalRequestRoute?._alreadyContinuedParams?.headers;
         requestPausedSessionInfo!.session._sendMayFail('Fetch.continueRequest', { requestId: requestPausedEvent.requestId, headers: headersOverride });
       } else {
-        route = new RouteImpl(requestPausedSessionInfo!.session, requestPausedEvent.requestId);
+        route = new RouteImpl(requestPausedSessionInfo!.session, requestPausedEvent.requestId, this._page, requestPausedEvent.networkId, this);
       }
     }
     const isNavigationRequest = requestWillBeSentEvent.requestId === requestWillBeSentEvent.loaderId && requestWillBeSentEvent.type === 'Document';
@@ -547,6 +551,8 @@
     if (request.session !== sessionInfo.session && !sessionInfo.isMain && request._documentId === request._requestId)
       request.session = sessionInfo.session;
   }
+
+  _alreadyTrackedNetworkIds: Set<string> = new Set();
 }
 
 class InterceptableRequest {
@@ -606,32 +612,83 @@
   _alreadyContinuedParams: Protocol.Fetch.continueRequestParameters | undefined;
   _fulfilled: boolean = false;
 
-  constructor(session: CRSession, interceptionId: string) {
+  constructor(session: CRSession, interceptionId: string, page: Page, networkId, sessionManager) {
+    this._sessionManager = void 0;
+    this._networkId = void 0;
+    this._page = void 0;
     this._session = session;
     this._interceptionId = interceptionId;
+    this._page = page;
+    this._networkId = networkId;
+    this._sessionManager = sessionManager;
+    eventsHelper.addEventListener(this._session, 'Fetch.requestPaused', async e => await this._networkRequestIntercepted(e));
   }
 
   async continue(overrides: types.NormalizedContinueOverrides): Promise<void> {
     this._alreadyContinuedParams = {
-      requestId: this._interceptionId!,
+      requestId: this._interceptionId,
       url: overrides.url,
       headers: overrides.headers,
       method: overrides.method,
-      postData: overrides.postData ? overrides.postData.toString('base64') : undefined
+      postData: overrides.postData ? overrides.postData.toString('base64') : undefined,
     };
-    await catchDisallowedErrors(async () => {
-      await this._session.send('Fetch.continueRequest', this._alreadyContinuedParams);
-    });
+    if (overrides.url && (overrides.url === 'http://patchright-init-script-inject.internal/' || overrides.url === 'https://patchright-init-script-inject.internal/')) {
+      await catchDisallowedErrors(async () => {
+        this._sessionManager._alreadyTrackedNetworkIds.add(this._networkId);
+        this._session.send('Fetch.continueRequest', { requestId: this._interceptionId, interceptResponse: true });
+      }) ;
+    } else {
+      await catchDisallowedErrors(async () => {
+        await this._session.send('Fetch.continueRequest', this._alreadyContinuedParams);
+      });
+    }
   }
 
   async fulfill(response: types.NormalizedFulfillResponse) {
+    const isTextHtml = response.resourceType === 'Document' || response.headers.some(header => header.name === 'content-type' && header.value.includes('text/html'));
+    var allInjections = [...this._page._delegate._mainFrameSession._evaluateOnNewDocumentScripts];
+        for (const binding of this._page._delegate._browserContext._pageBindings.values()) {
+          if (!allInjections.includes(binding)) allInjections.push(binding);
+        }
+    if (isTextHtml && allInjections.length) {
+      // I Chatted so hard for this Code
+      let scriptNonce = crypto.randomBytes(22).toString('hex');
+      for (let i = 0; i < response.headers.length; i++) {
+        if (response.headers[i].name === 'content-security-policy' || response.headers[i].name === 'content-security-policy-report-only') {
+          // Search for an existing script-src nonce that we can hijack
+          let cspValue = response.headers[i].value;
+          const nonceRegex = /script-src[^;]*'nonce-([\w-]+)'/;
+          const nonceMatch = cspValue.match(nonceRegex);
+          if (nonceMatch) {
+            scriptNonce = nonceMatch[1];
+          } else {
+            // Add the new nonce value to the script-src directive
+            const scriptSrcRegex = /(script-src[^;]*)(;|$)/;
+            const newCspValue = cspValue.replace(scriptSrcRegex, `$1 'nonce-${scriptNonce}'$2`);
+            response.headers[i].value = newCspValue;
+          }
+          break;
+        }
+      }
+      let injectionHTML = "";
+      allInjections.forEach((script) => {
+        let scriptId = crypto.randomBytes(22).toString('hex');
+        let scriptSource = script.source || script;
+        injectionHTML += `<script class="${this._page._delegate.initScriptTag}" nonce="${scriptNonce}" type="text/javascript">document.getElementById("${scriptId}")?.remove();${scriptSource}</script>`;
+      });
+      if (response.isBase64) {
+        response.isBase64 = false;
+        response.body = injectionHTML + Buffer.from(response.body, 'base64').toString('utf-8');
+      } else {
+        response.body = injectionHTML + response.body;
+      }
+    }
     this._fulfilled = true;
     const body = response.isBase64 ? response.body : Buffer.from(response.body).toString('base64');
-
     const responseHeaders = splitSetCookieHeader(response.headers);
     await catchDisallowedErrors(async () => {
       await this._session.send('Fetch.fulfillRequest', {
-        requestId: this._interceptionId!,
+        requestId: response.interceptionId ? response.interceptionId : this._interceptionId,
         responseCode: response.status,
         responsePhrase: network.statusText(response.status),
         responseHeaders,
@@ -650,6 +707,33 @@
       });
     });
   }
+
+  async _networkRequestIntercepted(event) {
+    if (event.resourceType !== 'Document') {
+      /*await catchDisallowedErrors(async () => {
+        await this._session.send('Fetch.continueRequest', { requestId: event.requestId });
+      });*/
+      return;
+    }
+    if (this._networkId != event.networkId || !this._sessionManager._alreadyTrackedNetworkIds.has(event.networkId)) return;
+    try {
+      if (event.responseStatusCode >= 301 && event.responseStatusCode <= 308  || (event.redirectedRequestId && !event.responseStatusCode)) {
+        await this._session.send('Fetch.continueRequest', { requestId: event.requestId, interceptResponse: true });
+      } else {
+        const responseBody = await this._session.send('Fetch.getResponseBody', { requestId: event.requestId });
+        await this.fulfill({
+          headers: event.responseHeaders,
+          isBase64: true,
+          body: responseBody.body,
+          status: event.responseStatusCode,
+          interceptionId: event.requestId,
+          resourceType: event.resourceType,
+        })
+      }
+    } catch (error) {
+      await this._session._sendMayFail('Fetch.continueRequest', { requestId: event.requestId });
+    }
+  }
 }
 
 // In certain cases, protocol will return error if the request was already canceled
diff -ruN playwright/packages/playwright-core/src/server/chromium/crPage.ts patchright/packages/playwright-core/src/server/chromium/crPage.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -101,7 +103,8 @@
     this.updateOffline();
     this.updateExtraHTTPHeaders();
     this.updateHttpCredentials();
-    this.updateRequestInterception();
+    this._networkManager.setRequestInterception(true);
+    this.initScriptTag = crypto.randomBytes(20).toString('hex');
     this._mainFrameSession = new FrameSession(this, client, targetId, null);
     this._sessions.set(targetId, this._mainFrameSession);
     if (opener && !browserContext._options.noDefaultViewport) {
@@ -231,10 +234,11 @@
   }
 
   async addInitScript(initScript: InitScript, world: types.World = 'main'): Promise<void> {
+    this._page.initScripts.push(initScript);
     await this._forAllFrameSessions(frame => frame._evaluateOnNewDocument(initScript, world));
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     await this._forAllFrameSessions(frame => frame._removeEvaluatesOnNewDocument());
   }
 
@@ -365,6 +369,15 @@
   shouldToggleStyleSheetToSyncAnimations(): boolean {
     return false;
   }
+
+  async exposeBinding(binding) {
+    await this._forAllFrameSessions(frame => frame._initBinding(binding));
+    await Promise.all(this._page.frames().map(frame => frame.evaluateExpression(binding.source).catch(e => {})));
+  }
+
+  async removeExposedBindings() {
+    await this._forAllFrameSessions(frame => frame._removeExposedBindings());
+  }
 }
 
 class FrameSession {
@@ -479,19 +492,6 @@
           this._handleFrameTree(frameTree);
           this._addRendererListeners();
         }
-
-        const localFrames = this._isMainFrame() ? this._page.frames() : [this._page._frameManager.frame(this._targetId)!];
-        for (const frame of localFrames) {
-          // Note: frames might be removed before we send these.
-          this._client._sendMayFail('Page.createIsolatedWorld', {
-            frameId: frame._id,
-            grantUniveralAccess: true,
-            worldName: UTILITY_WORLD_NAME,
-          });
-          for (const initScript of this._crPage._page.allInitScripts())
-            frame.evaluateExpression(initScript.source).catch(e => {});
-        }
-
         const isInitialEmptyPage = this._isMainFrame() && this._page.mainFrame().url() === ':';
         if (isInitialEmptyPage) {
           // Ignore lifecycle events, worlds and bindings for the initial empty page. It is never the final page
@@ -501,14 +501,20 @@
             this._eventListeners.push(eventsHelper.addEventListener(this._client, 'Page.lifecycleEvent', event => this._onLifecycleEvent(event)));
           });
         } else {
+          const localFrames = this._isMainFrame() ? this._page.frames() : [this._page._frameManager.frame(this._targetId)!];
+          for (const frame of localFrames) {
+            this._page._frameManager.frame(frame._id)._context("utility");
+            for (const binding of this._crPage._browserContext._pageBindings.values())
+              frame.evaluateExpression(binding.source).catch(e => {});
+            for (const source of this._crPage._browserContext.initScripts)
+              frame.evaluateExpression(source).catch(e => {});
+          }
           this._firstNonInitialNavigationCommittedFulfill();
           this._eventListeners.push(eventsHelper.addEventListener(this._client, 'Page.lifecycleEvent', event => this._onLifecycleEvent(event)));
         }
       }),
       this._client.send('Log.enable', {}),
       lifecycleEventsEnabled = this._client.send('Page.setLifecycleEventsEnabled', { enabled: true }),
-      this._client.send('Runtime.enable', {}),
-      this._client.send('Runtime.addBinding', { name: PageBinding.kPlaywrightBinding }),
       this._client.send('Page.addScriptToEvaluateOnNewDocument', {
         source: '',
         worldName: UTILITY_WORLD_NAME,
@@ -541,14 +547,16 @@
       promises.push(this._updateGeolocation(true));
       promises.push(this._updateEmulateMedia());
       promises.push(this._updateFileChooserInterception(true));
-      for (const initScript of this._crPage._page.allInitScripts())
-        promises.push(this._evaluateOnNewDocument(initScript, 'main'));
+      for (const binding of this._crPage._page.allBindings()) promises.push(this._initBinding(binding));
+      for (const initScript of this._crPage._browserContext.initScripts) promises.push(this._evaluateOnNewDocument(initScript, 'main'));
+      for (const initScript of this._crPage._page.initScripts) promises.push(this._evaluateOnNewDocument(initScript, 'main'));
       if (screencastOptions)
         promises.push(this._startVideoRecording(screencastOptions));
     }
-    promises.push(this._client.send('Runtime.runIfWaitingForDebugger'));
+    if (!(this._crPage._page._pageBindings.size || this._crPage._browserContext._pageBindings.size)) promises.push(this._client.send('Runtime.runIfWaitingForDebugger'));
     promises.push(this._firstNonInitialNavigationCommittedPromise);
     await Promise.all(promises);
+    if (this._crPage._page._pageBindings.size || this._crPage._browserContext._pageBindings.size) await this._client.send('Runtime.runIfWaitingForDebugger');
   }
 
   dispose() {
@@ -565,18 +573,31 @@
 
   async _navigate(frame: frames.Frame, url: string, referrer: string | undefined): Promise<frames.GotoResult> {
     const response = await this._client.send('Page.navigate', { url, referrer, frameId: frame._id, referrerPolicy: 'unsafeUrl' });
+    this._client._sendMayFail('Page.waitForDebugger');
     if (response.errorText)
       throw new frames.NavigationAbortedError(response.loaderId, `${response.errorText} at ${url}`);
     return { newDocumentId: response.loaderId };
   }
 
-  _onLifecycleEvent(event: Protocol.Page.lifecycleEventPayload) {
+  async _onLifecycleEvent(event: Protocol.Page.lifecycleEventPayload) {
     if (this._eventBelongsToStaleFrame(event.frameId))
       return;
     if (event.name === 'load')
       this._page._frameManager.frameLifecycleEvent(event.frameId, 'load');
     else if (event.name === 'DOMContentLoaded')
       this._page._frameManager.frameLifecycleEvent(event.frameId, 'domcontentloaded');
+    await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      var document = await this._client._sendMayFail("DOM.getDocument");
+      if (!document) return
+      var query = await this._client._sendMayFail("DOM.querySelectorAll", {
+        nodeId: document.root.nodeId,
+        selector: "[class=" + this._crPage.initScriptTag + "]"
+      });
+      if (!query) return
+      for (const nodeId of query.nodeIds) await this._client._sendMayFail("DOM.removeNode", { nodeId: nodeId });
+      await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      // ensuring execution context
+      try { await this._page._frameManager.frame(this._targetId)._context("utility") } catch { };
   }
 
   _handleFrameTree(frameTree: Protocol.Page.FrameTree) {
@@ -623,12 +644,24 @@
     this._page._frameManager.frameAttached(frameId, parentFrameId);
   }
 
-  _onFrameNavigated(framePayload: Protocol.Page.Frame, initial: boolean) {
+  async _onFrameNavigated(framePayload: Protocol.Page.Frame, initial: boolean) {
     if (this._eventBelongsToStaleFrame(framePayload.id))
       return;
     this._page._frameManager.frameCommittedNewDocumentNavigation(framePayload.id, framePayload.url + (framePayload.urlFragment || ''), framePayload.name || '', framePayload.loaderId, initial);
     if (!initial)
       this._firstNonInitialNavigationCommittedFulfill();
+    await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      var document = await this._client._sendMayFail("DOM.getDocument");
+      if (!document) return
+      var query = await this._client._sendMayFail("DOM.querySelectorAll", {
+        nodeId: document.root.nodeId,
+        selector: "[class=" + this._crPage.initScriptTag + "]"
+      });
+      if (!query) return
+      for (const nodeId of query.nodeIds) await this._client._sendMayFail("DOM.removeNode", { nodeId: nodeId });
+      await this._client._sendMayFail('Runtime.runIfWaitingForDebugger');
+      // ensuring execution context
+      try { await this._page._frameManager.frame(this._targetId)._context("utility") } catch { };
   }
 
   _onFrameRequestedNavigation(payload: Protocol.Page.frameRequestedNavigationPayload) {
@@ -665,19 +698,24 @@
   }
 
   _onExecutionContextCreated(contextPayload: Protocol.Runtime.ExecutionContextDescription) {
+    for (const name of this._exposedBindingNames) this._client._sendMayFail('Runtime.addBinding', { name: name, executionContextId: contextPayload.id });
     const frame = contextPayload.auxData ? this._page._frameManager.frame(contextPayload.auxData.frameId) : null;
+    if (contextPayload.auxData.type == "worker") throw new Error("ExecutionContext is worker");
     if (!frame || this._eventBelongsToStaleFrame(frame._id))
       return;
     const delegate = new CRExecutionContext(this._client, contextPayload);
-    let worldName: types.World|null = null;
-    if (contextPayload.auxData && !!contextPayload.auxData.isDefault)
-      worldName = 'main';
-    else if (contextPayload.name === UTILITY_WORLD_NAME)
-      worldName = 'utility';
+    let worldName = contextPayload.name;
     const context = new dom.FrameExecutionContext(delegate, frame, worldName);
     if (worldName)
       frame._contextCreated(worldName, context);
     this._contextIdToContext.set(contextPayload.id, context);
+    for (const source of this._exposedBindingScripts) {
+      this._client._sendMayFail("Runtime.evaluate", {
+        expression: source,
+        contextId: contextPayload.id,
+        awaitPromise: true,
+      })
+    }
   }
 
   _onExecutionContextDestroyed(executionContextId: number) {
@@ -693,7 +731,7 @@
       this._onExecutionContextDestroyed(contextId);
   }
 
-  _onAttachedToTarget(event: Protocol.Target.attachedToTargetPayload) {
+  async _onAttachedToTarget(event: Protocol.Target.attachedToTargetPayload) {
     const session = this._client.createChildSession(event.sessionId);
 
     if (event.targetInfo.type === 'iframe') {
@@ -725,8 +763,17 @@
     session.once('Runtime.executionContextCreated', async event => {
       worker._createExecutionContext(new CRExecutionContext(session, event.context));
     });
+    var globalThis = await session._sendMayFail('Runtime.evaluate', {
+      expression: "globalThis",
+      serializationOptions: { serialization: "idOnly" }
+
+    });
+    if (globalThis && globalThis.result) {
+      var globalThisObjId = globalThis.result.objectId;
+      var executionContextId = parseInt(globalThisObjId.split('.')[1], 10);
+      worker._createExecutionContext(new CRExecutionContext(session, { id: executionContextId }));
+    }
     // This might fail if the target is closed before we initialize.
-    session._sendMayFail('Runtime.enable');
     // TODO: attribute workers to the right frame.
     this._crPage._networkManager.addSession(session, this._page._frameManager.frame(this._targetId) ?? undefined).catch(() => {});
     session._sendMayFail('Runtime.runIfWaitingForDebugger');
@@ -805,8 +852,8 @@
     const pageOrError = await this._crPage._page.waitForInitializedOrError();
     if (!(pageOrError instanceof Error)) {
       const context = this._contextIdToContext.get(event.executionContextId);
-      if (context)
-        await this._page._onBindingCalled(event.payload, context);
+      if (context) await this._page._onBindingCalled(event.payload, context);
+      else await this._page._onBindingCalled(event.payload, (await this._page.mainFrame()._mainContext())) // This might be a bit sketchy but it works for now
     }
   }
 
@@ -1052,16 +1099,11 @@
   }
 
   async _evaluateOnNewDocument(initScript: InitScript, world: types.World): Promise<void> {
-    const worldName = world === 'utility' ? UTILITY_WORLD_NAME : undefined;
-    const { identifier } = await this._client.send('Page.addScriptToEvaluateOnNewDocument', { source: initScript.source, worldName });
-    if (!initScript.internal)
-      this._evaluateOnNewDocumentIdentifiers.push(identifier);
+    this._evaluateOnNewDocumentScripts.push(initScript)
   }
 
   async _removeEvaluatesOnNewDocument(): Promise<void> {
-    const identifiers = this._evaluateOnNewDocumentIdentifiers;
-    this._evaluateOnNewDocumentIdentifiers = [];
-    await Promise.all(identifiers.map(identifier => this._client.send('Page.removeScriptToEvaluateOnNewDocument', { identifier })));
+    this._evaluateOnNewDocumentScripts = [];
   }
 
   async _getContentFrame(handle: dom.ElementHandle): Promise<frames.Frame | null> {
@@ -1168,6 +1210,47 @@
       throw new Error(dom.kUnableToAdoptErrorMessage);
     return createHandle(to, result.object).asElement()!;
   }
+
+  _exposedBindingNames: string[] = [];
+  _evaluateOnNewDocumentScripts: string[] = [];
+  _parsedExecutionContextIds: number[] = [];
+  _exposedBindingScripts: string[] = [];
+
+  async _initBinding(binding = PageBinding) {
+    var result = await this._client._sendMayFail('Page.createIsolatedWorld', {
+      frameId: this._targetId, grantUniveralAccess: true, worldName: "utility"
+    });
+    if (!result) return
+    var isolatedContextId = result.executionContextId
+
+    var globalThis = await this._client._sendMayFail('Runtime.evaluate', {
+      expression: "globalThis",
+      serializationOptions: { serialization: "idOnly" }
+    });
+    if (!globalThis) return
+    var globalThisObjId = globalThis["result"]['objectId']
+    var mainContextId = parseInt(globalThisObjId.split('.')[1], 10);
+
+    await Promise.all([
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name }),
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name, executionContextId: mainContextId }),
+      this._client._sendMayFail('Runtime.addBinding', { name: binding.name, executionContextId: isolatedContextId }),
+      // this._client._sendMayFail("Runtime.evaluate", { expression: binding.source, contextId: mainContextId, awaitPromise: true })
+    ]);
+    this._exposedBindingNames.push(binding.name);
+    this._exposedBindingScripts.push(binding.source);
+    await this._crPage.addInitScript(binding.source);
+    //this._client._sendMayFail('Runtime.runIfWaitingForDebugger')
+  }
+
+  async _removeExposedBindings() {
+    const toRetain: string[] = [];
+    const toRemove: string[] = [];
+    for (const name of this._exposedBindingNames)
+      (name.startsWith('__pw_') ? toRetain : toRemove).push(name);
+    this._exposedBindingNames = toRetain;
+    await Promise.all(toRemove.map(name => this._client.send('Runtime.removeBinding', { name })));
+  }
 }
 
 async function emulateLocale(session: CRSession, locale: string) {
diff -ruN playwright/packages/playwright-core/src/server/chromium/crServiceWorker.ts patchright/packages/playwright-core/src/server/chromium/crServiceWorker.ts
---
+++
@@ -44,8 +44,6 @@
       this.updateOffline();
       this._networkManager.addSession(session, undefined, true /* isMain */).catch(() => {});
     }
-
-    session.send('Runtime.enable', {}).catch(e => { });
     session.send('Runtime.runIfWaitingForDebugger').catch(e => { });
     session.on('Inspector.targetReloadedAfterCrash', () => {
       // Resume service worker after restart.
diff -ruN playwright/packages/playwright-core/src/server/clock.ts patchright/packages/playwright-core/src/server/clock.ts
---
+++
@@ -92,6 +92,14 @@
   }
 
   private async _evaluateInFrames(script: string) {
+    // Dont ask me why this works
+    await Promise.all(this._browserContext.pages().map(async page => {
+      await Promise.all(page.frames().map(async frame => {
+        try {
+          await frame.evaluateExpression("");
+        } catch (e) {}
+      }));
+    }));
     await this._browserContext.safeNonStallingEvaluateInAllFrames(script, 'main', { throwOnJSErrors: true });
   }
 }
diff -ruN playwright/packages/playwright-core/src/server/dispatchers/frameDispatcher.ts patchright/packages/playwright-core/src/server/dispatchers/frameDispatcher.ts
---
+++
@@ -84,11 +84,15 @@
   }
 
   async evaluateExpression(params: channels.FrameEvaluateExpressionParams, metadata: CallMetadata): Promise<channels.FrameEvaluateExpressionResult> {
-    return { value: serializeResult(await this._frame.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._frame.evaluateExpression(params.expression, { isFunction: params.isFunction,
+      world: params.isolatedContext ? 'utility': 'main'
+    }, parseArgument(params.arg))) };
   }
 
   async evaluateExpressionHandle(params: channels.FrameEvaluateExpressionHandleParams, metadata: CallMetadata): Promise<channels.FrameEvaluateExpressionHandleResult> {
-    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._frame.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._frame.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction,
+      world: params.isolatedContext ? 'utility': 'main'
+    }, parseArgument(params.arg))) };
   }
 
   async waitForSelector(params: channels.FrameWaitForSelectorParams, metadata: CallMetadata): Promise<channels.FrameWaitForSelectorResult> {
diff -ruN playwright/packages/playwright-core/src/server/dispatchers/jsHandleDispatcher.ts patchright/packages/playwright-core/src/server/dispatchers/jsHandleDispatcher.ts
---
+++
@@ -39,11 +39,11 @@
   }
 
   async evaluateExpression(params: channels.JSHandleEvaluateExpressionParams): Promise<channels.JSHandleEvaluateExpressionResult> {
-    return { value: serializeResult(await this._object.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._object.evaluateExpression(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg), params.isolatedContext)) };
   }
 
   async evaluateExpressionHandle(params: channels.JSHandleEvaluateExpressionHandleParams): Promise<channels.JSHandleEvaluateExpressionHandleResult> {
-    const jsHandle = await this._object.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg));
+    const jsHandle = await this._object.evaluateExpressionHandle(params.expression, { isFunction: params.isFunction }, parseArgument(params.arg), params.isolatedContext);
     return { handle: ElementHandleDispatcher.fromJSHandle(this.parentScope(), jsHandle) };
   }
 
diff -ruN playwright/packages/playwright-core/src/server/dispatchers/pageDispatcher.ts patchright/packages/playwright-core/src/server/dispatchers/pageDispatcher.ts
---
+++
@@ -353,11 +353,11 @@
   }
 
   async evaluateExpression(params: channels.WorkerEvaluateExpressionParams, metadata: CallMetadata): Promise<channels.WorkerEvaluateExpressionResult> {
-    return { value: serializeResult(await this._object.evaluateExpression(params.expression, params.isFunction, parseArgument(params.arg))) };
+    return { value: serializeResult(await this._object.evaluateExpression(params.expression, params.isFunction, parseArgument(params.arg), params.isolatedContext)) };
   }
 
   async evaluateExpressionHandle(params: channels.WorkerEvaluateExpressionHandleParams, metadata: CallMetadata): Promise<channels.WorkerEvaluateExpressionHandleResult> {
-    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._object.evaluateExpressionHandle(params.expression, params.isFunction, parseArgument(params.arg))) };
+    return { handle: ElementHandleDispatcher.fromJSHandle(this, await this._object.evaluateExpressionHandle(params.expression, params.isFunction, parseArgument(params.arg), params.isolatedContext)) };
   }
 }
 
diff -ruN playwright/packages/playwright-core/src/server/firefox/ffPage.ts patchright/packages/playwright-core/src/server/firefox/ffPage.ts
---
+++
@@ -390,7 +390,7 @@
     await this._session.send('Page.setInitScripts', { scripts: this._initScripts.map(s => ({ script: s.initScript.source, worldName: s.worldName })) });
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     this._initScripts = this._initScripts.filter(s => s.initScript.internal);
     await this._session.send('Page.setInitScripts', { scripts: this._initScripts.map(s => ({ script: s.initScript.source, worldName: s.worldName })) });
   }
diff -ruN playwright/packages/playwright-core/src/server/frameSelectors.ts patchright/packages/playwright-core/src/server/frameSelectors.ts
---
+++
@@ -1,3 +1,5 @@
+// patchright - custom imports
+import { ElementHandle } from './dom';
 /**
  * Copyright (c) Microsoft Corporation.
  *
@@ -134,9 +136,29 @@
           throw injected.createStacklessError(`Selector "${selectorString}" resolved to ${injected.previewNode(element)}, <iframe> was expected`);
         return element;
       }, { info, scope: i === 0 ? scope : undefined, selectorString: stringifySelector(info.parsed) });
-      const element = handle.asElement() as ElementHandle<Element> | null;
-      if (!element)
-        return null;
+      let element = handle.asElement() as ElementHandle<Element> | null;
+      if (!element) {
+        try {
+          var client = frame._page._delegate._sessionForFrame(frame)._client;
+        } catch (e) {
+          var client = frame._page._delegate._mainFrameSession._client;
+        }
+        var mainContext = await frame._context("main");
+        const documentNode = await client.send("Runtime.evaluate", {
+          expression: "document",
+          serializationOptions: {
+            serialization: "idOnly"
+          },
+          contextId: mainContext.delegate._contextId
+        });
+        const documentScope = new ElementHandle(mainContext, documentNode.result.objectId);
+        var check = await this._customFindFramesByParsed(injectedScript, client, mainContext, documentScope, info.parsed);
+        if (check.length > 0) {
+          element = check[0];
+        } else {
+          return null;
+        }
+      }
       const maybeFrame = await frame._page._delegate.getContentFrame(element);
       element.dispose();
       if (!maybeFrame)
@@ -158,6 +180,123 @@
     const injected = await context.injectedScript();
     return { injected, info: resolved.info, frame: resolved.frame, scope: resolved.scope };
   }
+
+  async _customFindFramesByParsed(injected, client, context, documentScope, parsed) {
+    var parsedEdits = { ...parsed };
+    var currentScopingElements = [documentScope];
+    while (parsed.parts.length > 0) {
+      var part = parsed.parts.shift();
+      parsedEdits.parts = [part];
+      var elements = [];
+      var elementsIndexes = [];
+      if (part.name == "nth") {
+        const partNth = Number(part.body);
+        if (partNth > currentScopingElements.length || partNth < -currentScopingElements.length) {
+          return continuePolling;
+        } else {
+          currentScopingElements = [currentScopingElements.at(partNth)];
+          continue;
+        }
+      } else if (part.name == "internal:or") {
+        var orredElements = await this._customFindFramesByParsed(injected, client, context, documentScope, part.body.parsed);
+        elements = currentScopingElements.concat(orredElements);
+      } else if (part.name == "internal:and") {
+        var andedElements = await this._customFindFramesByParsed(injected, client, context, documentScope, part.body.parsed);
+        const backendNodeIds = new Set(andedElements.map((item) => item.backendNodeId));
+        elements = currentScopingElements.filter((item) => backendNodeIds.has(item.backendNodeId));
+      } else {
+        for (const scope of currentScopingElements) {
+          const describedScope = await client.send("DOM.describeNode", {
+            objectId: scope._objectId,
+            depth: -1,
+            pierce: true
+          });
+          var queryingElements = [];
+          let findClosedShadowRoots2 = function(node, results = []) {
+            if (!node || typeof node !== "object") return results;
+            if (node.shadowRoots && Array.isArray(node.shadowRoots)) {
+              for (const shadowRoot2 of node.shadowRoots) {
+                if (shadowRoot2.shadowRootType === "closed" && shadowRoot2.backendNodeId) {
+                  results.push(shadowRoot2.backendNodeId);
+                }
+                findClosedShadowRoots2(shadowRoot2, results);
+              }
+            }
+            if (node.nodeName !== "IFRAME" && node.children && Array.isArray(node.children)) {
+              for (const child of node.children) {
+                findClosedShadowRoots2(child, results);
+              }
+            }
+            return results;
+          };
+          var findClosedShadowRoots = findClosedShadowRoots2;
+          var shadowRootBackendIds = findClosedShadowRoots2(describedScope.node);
+          var shadowRoots = [];
+          for (var shadowRootBackendId of shadowRootBackendIds) {
+            var resolvedShadowRoot = await client.send("DOM.resolveNode", {
+              backendNodeId: shadowRootBackendId,
+              contextId: context.delegate._contextId
+            });
+            shadowRoots.push(new ElementHandle(context, resolvedShadowRoot.object.objectId));
+          }
+          for (var shadowRoot of shadowRoots) {
+            const shadowElements = await shadowRoot.evaluateHandleInUtility(([injected, node, { parsed: parsed2 }]) => {
+              const elements2 = injected.querySelectorAll(parsed2, node);
+              return elements2;
+            }, {
+              parsed: parsedEdits,
+            });
+            const shadowElementsAmount = await shadowElements.getProperty("length");
+            queryingElements.push([shadowElements, shadowElementsAmount, shadowRoot]);
+          }
+          const rootElements = await scope.evaluateHandleInUtility(([injected, node, { parsed: parsed2 }]) => {
+            const elements2 = injected.querySelectorAll(parsed2, node);
+            return elements2;
+          }, {
+            parsed: parsedEdits
+          });
+          const rootElementsAmount = await rootElements.getProperty("length");
+          queryingElements.push([rootElements, rootElementsAmount, injected]);
+          for (var queryedElement of queryingElements) {
+            var elementsToCheck = queryedElement[0];
+            var elementsAmount = await queryedElement[1].jsonValue();
+            var parentNode = queryedElement[2];
+            for (var i = 0; i < elementsAmount; i++) {
+              if (parentNode.constructor.name == "ElementHandle") {
+                var elementToCheck = await parentNode.evaluateHandleInUtility(([injected, node, { index, elementsToCheck: elementsToCheck2 }]) => {
+                  return elementsToCheck2[index];
+                }, { index: i, elementsToCheck });
+              } else {
+                var elementToCheck = await parentNode.evaluateHandle((injected, { index, elementsToCheck: elementsToCheck2 }) => {
+                  return elementsToCheck2[index];
+                }, { index: i, elementsToCheck });
+              }
+              elementToCheck.parentNode = parentNode;
+              var resolvedElement = await client.send("DOM.describeNode", {
+                objectId: elementToCheck._objectId,
+                depth: -1
+              });
+              elementToCheck.backendNodeId = resolvedElement.node.backendNodeId;
+              elements.push(elementToCheck);
+            }
+          }
+        }
+      }
+      currentScopingElements = [];
+      for (var element of elements) {
+        var elemIndex = element.backendNodeId;
+        var elemPos = elementsIndexes.findIndex((index) => index > elemIndex);
+        if (elemPos === -1) {
+          currentScopingElements.push(element);
+          elementsIndexes.push(elemIndex);
+        } else {
+          currentScopingElements.splice(elemPos, 0, element);
+          elementsIndexes.splice(elemPos, 0, elemIndex);
+        }
+      }
+    }
+    return currentScopingElements;
+  }
 }
 
 async function adoptIfNeeded<T extends Node>(handle: ElementHandle<T>, context: FrameExecutionContext): Promise<ElementHandle<T>> {
diff -ruN playwright/packages/playwright-core/src/server/frames.ts patchright/packages/playwright-core/src/server/frames.ts
---
+++
@@ -1,3 +1,7 @@
+// patchright - custom imports
+import { CRExecutionContext } from './chromium/crExecutionContext';
+import { FrameExecutionContext } from './dom';
+import crypto from 'crypto';
 /**
  * Copyright 2017 Google Inc. All rights reserved.
  * Modifications copyright (c) Microsoft Corporation.
@@ -530,6 +534,9 @@
   }
 
   _onClearLifecycle() {
+    this._isolatedWorld = undefined;
+    this._mainWorld = undefined;
+    this._iframeWorld = undefined;
     for (const event of this._firedLifecycleEvents)
       this.emit(Frame.Events.RemoveLifecycle, event);
     this._firedLifecycleEvents.clear();
@@ -743,12 +750,68 @@
     return this._page._delegate.getFrameElement(this);
   }
 
-  _context(world: types.World): Promise<dom.FrameExecutionContext> {
-    return this._contextData.get(world)!.contextPromise.then(contextOrDestroyedReason => {
-      if (contextOrDestroyedReason instanceof js.ExecutionContext)
-        return contextOrDestroyedReason;
-      throw new Error(contextOrDestroyedReason.destroyedReason);
-    });
+  async _context(world: types.World): Promise<dom.FrameExecutionContext> {
+    /* await this._page._delegate._mainFrameSession._client._sendMayFail('DOM.enable');
+        var globalDoc = await this._page._delegate._mainFrameSession._client._sendMayFail('DOM.getFrameOwner', { frameId: this._id });
+        if (globalDoc) {
+          await this._page._delegate._mainFrameSession._client._sendMayFail("DOM.resolveNode", { nodeId: globalDoc.nodeId })
+        } */
+
+        // if (this.isDetached()) throw new Error('Frame was detached');
+        try {
+          var client = this._page._delegate._sessionForFrame(this)._client
+        } catch (e) { var client = this._page._delegate._mainFrameSession._client }
+        var iframeExecutionContextId = await this._getFrameMainFrameContextId(client)
+
+        if (world == "main") {
+          // Iframe Only
+          if (this != this._page.mainFrame() && iframeExecutionContextId && this._iframeWorld == undefined) {
+            var executionContextId = iframeExecutionContextId
+            var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+            this._iframeWorld = new FrameExecutionContext(crContext, this, world)
+            this._page._delegate._mainFrameSession._onExecutionContextCreated({
+              id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+            })
+          } else if (this._mainWorld == undefined) {
+            var globalThis = await client._sendMayFail('Runtime.evaluate', {
+              expression: "globalThis",
+              serializationOptions: { serialization: "idOnly" }
+            });
+            if (!globalThis) { return }
+            var globalThisObjId = globalThis["result"]['objectId']
+            var executionContextId = parseInt(globalThisObjId.split('.')[1], 10);
+
+            var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+            this._mainWorld = new FrameExecutionContext(crContext, this, world)
+            this._page._delegate._mainFrameSession._onExecutionContextCreated({
+              id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+            })
+          }
+        }
+        if (world != "main" && this._isolatedWorld == undefined) {
+          world = "utility"
+          var result = await client._sendMayFail('Page.createIsolatedWorld', {
+            frameId: this._id, grantUniveralAccess: true, worldName: world
+          });
+          if (!result) {
+            // if (this.isDetached()) throw new Error("Frame was detached");
+            return
+          }
+          var executionContextId = result.executionContextId
+          var crContext = new CRExecutionContext(client, { id: executionContextId }, this._id)
+          this._isolatedWorld = new FrameExecutionContext(crContext, this, world)
+          this._page._delegate._mainFrameSession._onExecutionContextCreated({
+            id: executionContextId, origin: world, name: world, auxData: { isDefault: this === this._page.mainFrame(), type: 'isolated', frameId: this._id }
+          })
+        }
+
+        if (world != "main") {
+          return this._isolatedWorld;
+        } else if (this != this._page.mainFrame() && iframeExecutionContextId) {
+          return this._iframeWorld;
+        } else {
+          return this._mainWorld;
+        }
   }
 
   _mainContext(): Promise<dom.FrameExecutionContext> {
@@ -796,58 +859,49 @@
   }
 
   async waitForSelectorInternal(progress: Progress, selector: string, performActionPreChecks: boolean, options: types.WaitForElementOptions, scope?: dom.ElementHandle): Promise<dom.ElementHandle<Element> | null> {
-    const { state = 'visible' } = options;
-    const promise = this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-      if (performActionPreChecks)
-        await this._page.performActionPreChecks(progress);
-
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-      progress.throwIfAborted();
-      if (!resolved) {
-        if (state === 'hidden' || state === 'detached')
-          return null;
-        return continuePolling;
-      }
-      const result = await resolved.injected.evaluateHandle((injected, { info, root }) => {
-        if (root && !root.isConnected)
-          throw injected.createStacklessError('Element is not attached to the DOM');
-        const elements = injected.querySelectorAll(info.parsed, root || document);
-        const element: Element | undefined  = elements[0];
-        const visible = element ? injected.utils.isElementVisible(element) : false;
-        let log = '';
-        if (elements.length > 1) {
-          if (info.strict)
-            throw injected.strictModeViolationError(info.parsed, elements);
-          log = `  locator resolved to ${elements.length} elements. Proceeding with the first one: ${injected.previewNode(elements[0])}`;
-        } else if (element) {
-          log = `  locator resolved to ${visible ? 'visible' : 'hidden'} ${injected.previewNode(element)}`;
+    const {
+      state = 'visible'
+    } = options;
+    const promise = this._retryWithProgressIfNotConnected(progress, selector, options.strict, true, async handle => {
+      const attached = !!handle;
+      var visible = false;
+      if (attached) {
+        if (handle.parentNode.constructor.name == "ElementHandle") {
+          visible = await handle.parentNode.evaluateInUtility(([injected, node, { handle }]) => {
+            return handle ? injected.utils.isElementVisible(handle) : false;
+          }, {
+            handle
+          });
+        } else {
+          visible = await handle.parentNode.evaluate((injected, { handle }) => {
+            return handle ? injected.utils.isElementVisible(handle) : false;
+          }, {
+            handle
+          });
         }
-        return { log, element, visible, attached: !!element };
-      }, { info: resolved.info, root: resolved.frame === this ? scope : undefined });
-      const { log, visible, attached } = await result.evaluate(r => ({ log: r.log, visible: r.visible, attached: r.attached }));
-      if (log)
-        progress.log(log);
-      const success = { attached, detached: !attached, visible, hidden: !visible }[state];
+      }
+
+      const success = {
+        attached,
+        detached: !attached,
+        visible,
+        hidden: !visible
+      }[state];
       if (!success) {
-        result.dispose();
-        return continuePolling;
+        return "internal:continuepolling";
       }
       if (options.omitReturnValue) {
-        result.dispose();
         return null;
       }
-      const element = state === 'attached' || state === 'visible' ? await result.evaluateHandle(r => r.element) : null;
-      result.dispose();
-      if (!element)
-        return null;
-      if ((options as any).__testHookBeforeAdoptNode)
-        await (options as any).__testHookBeforeAdoptNode();
+      const element = state === 'attached' || state === 'visible' ? handle : null;
+      if (!element) return null;
+      if (options.__testHookBeforeAdoptNode) await options.__testHookBeforeAdoptNode();
       try {
-        return await element._adoptTo(await resolved.frame._mainContext());
+        return element;
       } catch (e) {
-        return continuePolling;
+        return "internal:continuepolling";
       }
-    });
+    }, "returnOnNotResolved");
     return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
   }
 
@@ -886,7 +940,22 @@
   }
 
   async queryCount(selector: string): Promise<number> {
-    return await this.selectors.queryCount(selector);
+    const custom_metadata = {
+      "internal": false,
+      "log": []
+    };
+    const controller = new ProgressController(custom_metadata, this);
+    const resultPromise = await controller.run(async progress => {
+      progress.log("waiting for " + this._asLocator(selector));
+      const promise = await this._retryWithProgressIfNotConnected(progress, selector, false, false, async result => {
+        if (!result) return 0;
+        const handle = result[0];
+        const handles = result[1];
+        return handle ? handles.length : 0;
+      }, 'returnAll');
+      return promise;
+    }, 10000); // A bit geeky but its okay :D
+    return resultPromise ? resultPromise : 0;
   }
 
   async content(): Promise<string> {
@@ -908,31 +977,38 @@
   }
 
   async setContent(metadata: CallMetadata, html: string, options: types.NavigateOptions = {}): Promise<void> {
-    const controller = new ProgressController(metadata, this);
-    return controller.run(async progress => {
-      await this.raceNavigationAction(progress, options, async () => {
-        const waitUntil = options.waitUntil === undefined ? 'load' : options.waitUntil;
-        progress.log(`setting frame content, waiting until "${waitUntil}"`);
-        const tag = `--playwright--set--content--${this._id}--${++this._setContentCounter}--`;
-        const context = await this._utilityContext();
-        const lifecyclePromise = new Promise((resolve, reject) => {
-          this._page._frameManager._consoleMessageTags.set(tag, () => {
-            // Clear lifecycle right after document.open() - see 'tag' below.
-            this._onClearLifecycle();
-            this._waitForLoadState(progress, waitUntil).then(resolve).catch(reject);
+      const controller = new ProgressController(metadata, this);
+      return controller.run(async progress => {
+        await this.raceNavigationAction(progress, options, async () => {
+          const waitUntil = options.waitUntil === undefined ? 'load' : options.waitUntil;
+          progress.log(`setting frame content, waiting until "${waitUntil}"`);
+          const tag = `--playwright--set--content--${this._id}--${++this._setContentCounter}--`;
+          const bindingName = "_tagDebug" + crypto.randomBytes(20).toString('hex');
+          const context = await this._utilityContext();
+          await this._page._delegate._mainFrameSession._client.send('Runtime.addBinding', { name: bindingName });
+          const lifecyclePromise = new Promise(async (resolve, reject) => {
+            await this._page.exposeBinding(bindingName, false, (tag) => {
+              this._onClearLifecycle();
+              this._waitForLoadState(progress, waitUntil).then(resolve).catch(reject);
+            });
+          });
+          const contentPromise = context.evaluate(({ html, tag, bindingName }) => {
+            document.open();
+            var _tagDebug = window[bindingName].bind({});
+            delete window[bindingName]
+            _tagDebug('{ "name": "' + bindingName + '", "seq": 1, "serializedArgs": ["' + tag + '"] }');
+            console.debug(tag);  // eslint-disable-line no-console
+            document.write(html);
+            document.close();
+          }, { html, tag,
+            bindingName
           });
+          await Promise.all([contentPromise, lifecyclePromise]);
+          return null;
         });
-        const contentPromise = context.evaluate(({ html, tag }) => {
-          document.open();
-          console.debug(tag);  // eslint-disable-line no-console
-          document.write(html);
-          document.close();
-        }, { html, tag });
-        await Promise.all([contentPromise, lifecyclePromise]);
-        return null;
-      });
-    }, this._page._timeoutSettings.navigationTimeout(options));
-  }
+      }, this._page._timeoutSettings.navigationTimeout(options));
+    }
+
 
   name(): string {
     return this._name || '';
@@ -1125,50 +1201,80 @@
     selector: string,
     strict: boolean | undefined,
     performActionPreChecks: boolean,
-    action: (handle: dom.ElementHandle<Element>) => Promise<R | 'error:notconnected'>): Promise<R> {
-    progress.log(`waiting for ${this._asLocator(selector)}`);
+    action: (handle: dom.ElementHandle<Element>) => Promise<R | 'error:notconnected'>, returnAction: boolean | undefined): Promise<R> {
+    progress.log("waiting for " + this._asLocator(selector));
     return this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-      if (performActionPreChecks)
-        await this._page.performActionPreChecks(progress);
-
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, { strict });
+      if (performActionPreChecks) await this._page.performActionPreChecks(progress);
+      const resolved = await this.selectors.resolveInjectedForSelector(selector, {
+        strict
+      });
       progress.throwIfAborted();
-      if (!resolved)
+      if (!resolved) {
+        if (returnAction === 'returnOnNotResolved' || returnAction === 'returnAll') {
+        const result = await action(null);
+        return result === "internal:continuepolling" ? continuePolling2 : result;
+      }
+        return continuePolling;
+      }
+
+      try {
+        var client = this._page._delegate._sessionForFrame(resolved.frame)._client;
+      } catch (e) {
+        var client = this._page._delegate._mainFrameSession._client;
+      }
+      var context = await resolved.frame._context("main");
+
+      const documentNode = await client.send('Runtime.evaluate', {
+        expression: "document",
+        serializationOptions: {
+          serialization: "idOnly"
+        },
+        contextId: context.delegate._contextId,
+      });
+      const documentScope = new dom.ElementHandle(context, documentNode.result.objectId);
+
+      const currentScopingElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, resolved.info.parsed);
+      if (currentScopingElements.length == 0) {
+        // TODO: Dispose?
+        if (returnAction === 'returnOnNotResolved' || returnAction === 'returnAll') {
+        const result = await action(null);
+        return result === "internal:continuepolling" ? continuePolling2 : result;
+      }
         return continuePolling;
-      const result = await resolved.injected.evaluateHandle((injected, { info, callId }) => {
-        const elements = injected.querySelectorAll(info.parsed, document);
-        if (callId)
-          injected.markTargetElements(new Set(elements), callId);
-        const element = elements[0] as Element | undefined;
-        let log = '';
-        if (elements.length > 1) {
-          if (info.strict)
+      }
+      const resultElement = currentScopingElements[0];
+      if (currentScopingElements.length > 1) {
+        if (resolved.info.strict) {
+          await resolved.injected.evaluateHandle((injected, {
+            info,
+            elements
+          }) => {
             throw injected.strictModeViolationError(info.parsed, elements);
-          log = `  locator resolved to ${elements.length} elements. Proceeding with the first one: ${injected.previewNode(elements[0])}`;
-        } else if (element) {
-          log = `  locator resolved to ${injected.previewNode(element)}`;
+          }, {
+            info: resolved.info,
+            elements: currentScopingElements
+          });
         }
-        return { log, success: !!element, element };
-      }, { info: resolved.info, callId: progress.metadata.id });
-      const { log, success } = await result.evaluate(r => ({ log: r.log, success: r.success }));
-      if (log)
-        progress.log(log);
-      if (!success) {
-        result.dispose();
-        return continuePolling;
+        progress.log("  locator resolved to " + currentScopingElements.length + " elements. Proceeding with the first one: " + resultElement.preview());
+      } else if (resultElement) {
+        progress.log("  locator resolved to " + resultElement.preview());
       }
-      const element = await result.evaluateHandle(r => r.element) as dom.ElementHandle<Element>;
-      result.dispose();
+
       try {
-        const result = await action(element);
+        var result = null;
+        if (returnAction === 'returnAll') {
+          result = await action([resultElement, currentScopingElements]);
+        } else {
+          result = await action(resultElement);
+        }
         if (result === 'error:notconnected') {
           progress.log('element was detached from the DOM, retrying');
           return continuePolling;
+        } else if (result === 'internal:continuepolling') {
+          return continuePolling;
         }
         return result;
-      } finally {
-        element?.dispose();
-      }
+      } finally { }
     });
   }
 
@@ -1319,17 +1425,35 @@
 
   async isVisibleInternal(selector: string, options: types.StrictOptions = {}, scope?: dom.ElementHandle): Promise<boolean> {
     try {
-      const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-      if (!resolved)
-        return false;
-      return await resolved.injected.evaluate((injected, { info, root }) => {
-        const element = injected.querySelector(info.parsed, root || document, info.strict);
-        const state = element ? injected.elementState(element, 'visible') : { matches: false, received: 'error:notconnected' };
-        return state.matches;
-      }, { info: resolved.info, root: resolved.frame === this ? scope : undefined });
+      const custom_metadata = { "internal": false, "log": [] };
+      const controller = new ProgressController(custom_metadata, this);
+      return await controller.run(async progress => {
+        progress.log("waiting for " + this._asLocator(selector));
+        const promise = this._retryWithProgressIfNotConnected(progress, selector, options.strict, false, async handle => {
+          if (!handle) return false;
+          if (handle.parentNode.constructor.name == "ElementHandle") {
+            return await handle.parentNode.evaluateInUtility(([injected, node, { handle }]) => {
+              const state = handle ? injected.elementState(handle, 'visible') : {
+                matches: false,
+                received: 'error:notconnected'
+              };
+              return state.matches;
+            }, { handle });
+          } else {
+            return await handle.parentNode.evaluate((injected, { handle }) => {
+              const state = handle ? injected.elementState(handle, 'visible') : {
+                matches: false,
+                received: 'error:notconnected'
+              };
+              return state.matches;
+            }, { handle });
+          }
+        }, "returnOnNotResolved");
+
+        return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
+      }, 10000) || false; // A bit geeky but its okay :D
     } catch (e) {
-      if (js.isJavaScriptErrorInEvaluate(e) || isInvalidSelectorError(e) || isSessionClosedError(e))
-        throw e;
+      if (js.isJavaScriptErrorInEvaluate(e) || isInvalidSelectorError(e) || isSessionClosedError(e)) throw e;
       return false;
     }
   }
@@ -1489,40 +1613,46 @@
   }
 
   private async _expectInternal(progress: Progress, selector: string, options: FrameExpectParams, lastIntermediateResult: { received?: any, isSet: boolean }) {
-    const selectorInFrame = await this.selectors.resolveFrameForSelector(selector, { strict: true });
-    progress.throwIfAborted();
+    progress.log("waiting for " + this._asLocator(selector));
+    const isArray = options.expression === 'to.have.count' || options.expression.endsWith('.array');
 
-    const { frame, info } = selectorInFrame || { frame: this, info: undefined };
-    const world = options.expression === 'to.have.property' ? 'main' : (info?.world ?? 'utility');
-    const context = await frame._context(world);
-    const injected = await context.injectedScript();
-    progress.throwIfAborted();
+    const promise = await this._retryWithProgressIfNotConnected(progress, selector, !isArray, false, async result => {
+      const handle = result[0];
+      const handles = result[1];
+
+      if (handle.parentNode.constructor.name == "ElementHandle") {
+        return await handle.parentNode.evaluateInUtility(async ([injected, node, { handle, options, handles }]) => {
+          return await injected.expect(handle, options, handles);
+        }, { handle, options, handles });
+      } else {
+        return await handle.parentNode.evaluate(async (injected, { handle, options, handles }) => {
+          return await injected.expect(handle, options, handles);
+        }, { handle, options, handles });
+      }
+    }, 'returnAll');
 
-    const { log, matches, received, missingReceived } = await injected.evaluate(async (injected, { info, options, callId }) => {
-      const elements = info ? injected.querySelectorAll(info.parsed, document) : [];
-      if (callId)
-        injected.markTargetElements(new Set(elements), callId);
-      const isArray = options.expression === 'to.have.count' || options.expression.endsWith('.array');
-      let log = '';
-      if (isArray)
-        log = `  locator resolved to ${elements.length} element${elements.length === 1 ? '' : 's'}`;
-      else if (elements.length > 1)
-        throw injected.strictModeViolationError(info!.parsed, elements);
-      else if (elements.length)
-        log = `  locator resolved to ${injected.previewNode(elements[0])}`;
-      return { log, ...await injected.expect(elements[0], options, elements) };
-    }, { info, options, callId: progress.metadata.id });
-
-    if (log)
-      progress.log(log);
-    // Note: missingReceived avoids `unexpected value "undefined"` when element was not found.
+    // Default Values, if no Elements found
+    var matches = false;
+    var received = 0;
+    var missingReceived = null;
+    if (promise) {
+      matches = promise.matches;
+      received = promise.received;
+      missingReceived = promise.missingReceived;
+    } else if (options.expectedNumber === 0) {
+      matches = true;
+    }
+
+    // Note: missingReceived avoids unexpected value "undefined" when element was not found.
     if (matches === options.isNot) {
       lastIntermediateResult.received = missingReceived ? '<element(s) not found>' : received;
       lastIntermediateResult.isSet = true;
-      if (!missingReceived && !Array.isArray(received))
-        progress.log(`  unexpected value "${renderUnexpectedValue(options.expression, received)}"`);
+      if (!missingReceived && !Array.isArray(received)) progress.log('  unexpected value "' + renderUnexpectedValue(options.expression, received) + '"');
     }
-    return { matches, received };
+    return {
+      matches,
+      received
+    };
   }
 
   async _waitForFunctionExpression<R>(metadata: CallMetadata, expression: string, isFunction: boolean | undefined, arg: any, options: types.WaitForFunctionOptions, world: types.World = 'main'): Promise<js.SmartHandle<R>> {
@@ -1632,28 +1762,27 @@
     const callbackText = body.toString();
     const controller = new ProgressController(metadata, this);
     return controller.run(async progress => {
-      progress.log(`waiting for ${this._asLocator(selector)}`);
-      const promise = this.retryWithProgressAndTimeouts(progress, [0, 20, 50, 100, 100, 500], async continuePolling => {
-        const resolved = await this.selectors.resolveInjectedForSelector(selector, options, scope);
-        progress.throwIfAborted();
-        if (!resolved)
-          return continuePolling;
-        const { log, success, value } = await resolved.injected.evaluate((injected, { info, callbackText, taskData, callId, root }) => {
-          const callback = injected.eval(callbackText) as ElementCallback<T, R>;
-          const element = injected.querySelector(info.parsed, root || document, info.strict);
-          if (!element)
-            return { success: false };
-          const log = `  locator resolved to ${injected.previewNode(element)}`;
-          if (callId)
-            injected.markTargetElements(new Set([element]), callId);
-          return { log, success: true, value: callback(injected, element, taskData as T) };
-        }, { info: resolved.info, callbackText, taskData, callId: progress.metadata.id, root: resolved.frame === this ? scope : undefined });
-
-        if (log)
-          progress.log(log);
-        if (!success)
-          return continuePolling;
-        return value!;
+      progress.log("waiting for "+ this._asLocator(selector));
+      const promise = this._retryWithProgressIfNotConnected(progress, selector, false, false, async handle => {
+        if (handle.parentNode.constructor.name == "ElementHandle") {
+          return await handle.parentNode.evaluateInUtility(([injected, node, { callbackText, handle, taskData }]) => {
+            const callback = injected.eval(callbackText);
+            return callback(injected, handle, taskData);
+          }, {
+            callbackText,
+            handle,
+            taskData
+          });
+        } else {
+          return await handle.parentNode.evaluate((injected, { callbackText, handle, taskData }) => {
+            const callback = injected.eval(callbackText);
+            return callback(injected, handle, taskData);
+          }, {
+            callbackText,
+            handle,
+            taskData
+          });
+        }
       });
       return scope ? scope._context._raceAgainstContextDestroyed(promise) : promise;
     }, this._page._timeoutSettings.timeout(options));
@@ -1757,6 +1886,163 @@
   private _asLocator(selector: string) {
     return asLocator(this._page.attribution.playwright.options.sdkLanguage, selector);
   }
+
+  _isolatedWorld: dom.FrameExecutionContext;
+  _mainWorld: dom.FrameExecutionContext;
+  _iframeWorld: dom.FrameExecutionContext;
+
+  async _getFrameMainFrameContextId(client): Promise<number> {
+    try {
+        var globalDocument = await client._sendMayFail("DOM.getFrameOwner", {frameId: this._id,});
+        if (globalDocument && globalDocument.nodeId) {
+          var describedNode = await client._sendMayFail("DOM.describeNode", {
+            backendNodeId: globalDocument.backendNodeId,
+          });
+          if (describedNode) {
+            var resolvedNode = await client._sendMayFail("DOM.resolveNode", {
+              nodeId: describedNode.node.contentDocument.nodeId,
+            });
+            var _executionContextId = parseInt(resolvedNode.object.objectId.split(".")[1], 10);
+            return _executionContextId;
+            }
+          }
+        } catch (e) {}
+        return 0;
+  }
+
+  async _customFindElementsByParsed(resolved, client, context, documentScope, progress, parsed) {
+    var parsedEdits = { ...parsed };
+    // Note: We start scoping at document level
+    var currentScopingElements = [documentScope];
+    while (parsed.parts.length > 0) {
+      var part = parsed.parts.shift();
+      parsedEdits.parts = [part];
+      // Getting All Elements
+      var elements = [];
+      var elementsIndexes = [];
+
+      if (part.name == "nth") {
+        const partNth = Number(part.body);
+        if (partNth > currentScopingElements.length || partNth < -currentScopingElements.length) {
+          return continuePolling;
+        } else {
+          currentScopingElements = [currentScopingElements.at(partNth)];
+          continue;
+        }
+      } else if (part.name == "internal:or") {
+        var orredElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, part.body.parsed);
+        elements = currentScopingElements.concat(orredElements);
+      } else if (part.name == "internal:and") {
+        var andedElements = await this._customFindElementsByParsed(resolved, client, context, documentScope, progress, part.body.parsed);
+        const backendNodeIds = new Set(andedElements.map(item => item.backendNodeId));
+        elements = currentScopingElements.filter(item => backendNodeIds.has(item.backendNodeId));
+      } else {
+        for (const scope of currentScopingElements) {
+          const describedScope = await client.send('DOM.describeNode', {
+            objectId: scope._objectId,
+            depth: -1,
+            pierce: true
+          });
+
+          // Elements Queryed in the "current round"
+          var queryingElements = [];
+          function findClosedShadowRoots(node, results = []) {
+            if (!node || typeof node !== 'object') return results;
+            if (node.shadowRoots && Array.isArray(node.shadowRoots)) {
+              for (const shadowRoot of node.shadowRoots) {
+                if (shadowRoot.shadowRootType === 'closed' && shadowRoot.backendNodeId) {
+                  results.push(shadowRoot.backendNodeId);
+                }
+                findClosedShadowRoots(shadowRoot, results);
+              }
+            }
+            if (node.nodeName !== 'IFRAME' && node.children && Array.isArray(node.children)) {
+              for (const child of node.children) {
+                findClosedShadowRoots(child, results);
+              }
+            }
+            return results;
+          }
+
+          var shadowRootBackendIds = findClosedShadowRoots(describedScope.node);
+          var shadowRoots = [];
+          for (var shadowRootBackendId of shadowRootBackendIds) {
+            var resolvedShadowRoot = await client.send('DOM.resolveNode', {
+              backendNodeId: shadowRootBackendId,
+              contextId: context.delegate._contextId
+            });
+            shadowRoots.push(new dom.ElementHandle(context, resolvedShadowRoot.object.objectId));
+          }
+
+          for (var shadowRoot of shadowRoots) {
+            const shadowElements = await shadowRoot.evaluateHandleInUtility(([injected, node, { parsed, callId }]) => {
+             const elements = injected.querySelectorAll(parsed, node);
+              if (callId) injected.markTargetElements(new Set(elements), callId);
+              return elements
+            }, {
+              parsed: parsedEdits,
+              callId: progress.metadata.id
+            });
+
+            const shadowElementsAmount = await shadowElements.getProperty("length");
+            queryingElements.push([shadowElements, shadowElementsAmount, shadowRoot]);
+          }
+
+          // Document Root Elements (not in CSR)
+          const rootElements = await scope.evaluateHandleInUtility(([injected, node, { parsed, callId }]) => {
+            const elements = injected.querySelectorAll(parsed, node);
+            if (callId) injected.markTargetElements(new Set(elements), callId);
+            return elements
+          }, {
+            parsed: parsedEdits,
+            callId: progress.metadata.id
+          });
+          const rootElementsAmount = await rootElements.getProperty("length");
+          queryingElements.push([rootElements, rootElementsAmount, resolved.injected]);
+
+          // Querying and Sorting the elements by their backendNodeId
+          for (var queryedElement of queryingElements) {
+            var elementsToCheck = queryedElement[0];
+            var elementsAmount = await queryedElement[1].jsonValue();
+            var parentNode = queryedElement[2];
+            for (var i = 0; i < elementsAmount; i++) {
+              if (parentNode.constructor.name == "ElementHandle") {
+                var elementToCheck = await parentNode.evaluateHandleInUtility(([injected, node, { index, elementsToCheck }]) => { return elementsToCheck[index]; }, { index: i, elementsToCheck: elementsToCheck });
+              } else {
+                var elementToCheck = await parentNode.evaluateHandle((injected, { index, elementsToCheck }) => { return elementsToCheck[index]; }, { index: i, elementsToCheck: elementsToCheck });
+              }
+              // For other Functions/Utilities
+              elementToCheck.parentNode = parentNode;
+              var resolvedElement = await client.send('DOM.describeNode', {
+                objectId: elementToCheck._objectId,
+                depth: -1,
+              });
+              // Note: Possible Bug, Maybe well actually have to check the Documents Node Position instead of using the backendNodeId
+              elementToCheck.backendNodeId = resolvedElement.node.backendNodeId;
+              elements.push(elementToCheck);
+            }
+          }
+        }
+      }
+      // Setting currentScopingElements to the elements we just queried
+      currentScopingElements = [];
+      for (var element of elements) {
+        var elemIndex = element.backendNodeId;
+        // Sorting the Elements by their occourance in the DOM
+        var elemPos = elementsIndexes.findIndex(index => index > elemIndex);
+
+        // Sort the elements by their backendNodeId
+        if (elemPos === -1) {
+          currentScopingElements.push(element);
+          elementsIndexes.push(elemIndex);
+        } else {
+          currentScopingElements.splice(elemPos, 0, element);
+          elementsIndexes.splice(elemPos, 0, elemIndex);
+        }
+      }
+    }
+    return currentScopingElements;
+  }
 }
 
 class SignalBarrier {
diff -ruN playwright/packages/playwright-core/src/server/javascript.ts patchright/packages/playwright-core/src/server/javascript.ts
---
+++
@@ -154,17 +154,33 @@
     return evaluate(this._context, false /* returnByValue */, pageFunction, this, arg);
   }
 
-  async evaluateExpression(expression: string, options: { isFunction?: boolean }, arg: any) {
-    const value = await evaluateExpression(this._context, expression, { ...options, returnByValue: true }, this, arg);
-    await this._context.doSlowMo();
-    return value;
-  }
+  async evaluateExpression(expression: string, options: { isFunction?: boolean }, arg: any, isolatedContext?: boolean) {
+    let context = this._context;
+      if (context.constructor.name === "FrameExecutionContext") {
+          const frame = context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      const value = await evaluateExpression(context, expression, { ...options, returnByValue: true }, this, arg);
+      await context.doSlowMo();
+      return value;
+    }
 
-  async evaluateExpressionHandle(expression: string, options: { isFunction?: boolean }, arg: any): Promise<JSHandle<any>> {
-    const value = await evaluateExpression(this._context, expression, { ...options, returnByValue: false }, this, arg);
-    await this._context.doSlowMo();
-    return value;
-  }
+  async evaluateExpressionHandle(expression: string, options: { isFunction?: boolean }, arg: any, isolatedContext?: boolean): Promise<JSHandle<any>> {
+    let context = this._context;
+      if (this._context.constructor.name === "FrameExecutionContext") {
+          const frame = this._context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      const value = await evaluateExpression(context, expression, { ...options, returnByValue: false }, this, arg);
+      await context.doSlowMo();
+      return value;
+    }
 
   async getProperty(propertyName: string): Promise<JSHandle> {
     const objectHandle = await this.evaluateHandle((object: any, propertyName) => {
diff -ruN playwright/packages/playwright-core/src/server/page.ts patchright/packages/playwright-core/src/server/page.ts
---
+++
@@ -61,7 +61,7 @@
   goForward(): Promise<boolean>;
   requestGC(): Promise<void>;
   addInitScript(initScript: InitScript): Promise<void>;
-  removeNonInternalInitScripts(): Promise<void>;
+  removeInitScripts(): Promise<void>;
   closePage(runBeforeUnload: boolean): Promise<void>;
 
   navigateFrame(frame: frames.Frame, url: string, referrer: string | undefined): Promise<frames.GotoResult>;
@@ -352,15 +352,15 @@
       throw new Error(`Function "${name}" has been already registered in the browser context`);
     const binding = new PageBinding(name, playwrightBinding, needsHandle);
     this._pageBindings.set(name, binding);
-    await this._delegate.addInitScript(binding.initScript);
-    await Promise.all(this.frames().map(frame => frame.evaluateExpression(binding.initScript.source).catch(e => {})));
+    await this._delegate.exposeBinding(binding);
   }
 
   async _removeExposedBindings() {
-    for (const [key, binding] of this._pageBindings) {
-      if (!binding.internal)
+    for (const key of this._pageBindings.keys()) {
+      if (!key.startsWith('__pw'))
         this._pageBindings.delete(key);
     }
+    await this._delegate.removeExposedBindings();
   }
 
   setExtraHTTPHeaders(headers: types.HeadersArray) {
@@ -577,8 +577,8 @@
   }
 
   async _removeInitScripts() {
-    this.initScripts = this.initScripts.filter(script => script.internal);
-    await this._delegate.removeNonInternalInitScripts();
+    this.initScripts.splice(0, this.initScripts.length);
+    await this._delegate.removeInitScripts();
   }
 
   needsRequestInterception(): boolean {
@@ -770,11 +770,6 @@
       this._browserContext.addVisitedOrigin(origin);
   }
 
-  allInitScripts() {
-    const bindings = [...this._browserContext._pageBindings.values(), ...this._pageBindings.values()];
-    return [kBuiltinsScript, ...bindings.map(binding => binding.initScript), ...this._browserContext.initScripts, ...this.initScripts];
-  }
-
   getBinding(name: string) {
     return this._pageBindings.get(name) || this._browserContext._pageBindings.get(name);
   }
@@ -811,6 +806,10 @@
   markAsServerSideOnly() {
     this._isServerSideOnly = true;
   }
+
+  allBindings() {
+    return [...this._browserContext._pageBindings.values(), ...this._pageBindings.values()];
+  }
 }
 
 export class Worker extends SdkObject {
@@ -848,30 +847,42 @@
     this.openScope.close(new Error('Worker closed'));
   }
 
-  async evaluateExpression(expression: string, isFunction: boolean | undefined, arg: any): Promise<any> {
-    return js.evaluateExpression(await this._executionContextPromise, expression, { returnByValue: true, isFunction }, arg);
-  }
+  async evaluateExpression(expression: string, isFunction: boolean | undefined, arg: any, isolatedContext?: boolean): Promise<any> {
+    let context = await this._executionContextPromise;
+      if (context.constructor.name === "FrameExecutionContext") {
+          const frame = context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      return js.evaluateExpression(context, expression, { returnByValue: true, isFunction }, arg);
+    }
 
-  async evaluateExpressionHandle(expression: string, isFunction: boolean | undefined, arg: any): Promise<any> {
-    return js.evaluateExpression(await this._executionContextPromise, expression, { returnByValue: false, isFunction }, arg);
-  }
+  async evaluateExpressionHandle(expression: string, isFunction: boolean | undefined, arg: any, isolatedContext?: boolean): Promise<any> {
+    let context = await this._executionContextPromise;
+      if (this._context.constructor.name === "FrameExecutionContext") {
+          const frame = this._context.frame;
+          if (frame) {
+              if (isolatedContext) context = await frame._utilityContext();
+              else if (!isolatedContext) context = await frame._mainContext();
+          }
+      }
+      return js.evaluateExpression(context, expression, { returnByValue: false, isFunction }, arg);
+    }
 }
 
 export class PageBinding {
-  static kPlaywrightBinding = '__playwright__binding__';
-
   readonly name: string;
   readonly playwrightFunction: frames.FunctionWithSource;
-  readonly initScript: InitScript;
   readonly needsHandle: boolean;
   readonly internal: boolean;
 
   constructor(name: string, playwrightFunction: frames.FunctionWithSource, needsHandle: boolean) {
     this.name = name;
     this.playwrightFunction = playwrightFunction;
-    this.initScript = new InitScript(createPageBindingScript(PageBinding.kPlaywrightBinding, name, needsHandle), true /* internal */);
+    this.source = createPageBindingScript(name, needsHandle);
     this.needsHandle = needsHandle;
-    this.internal = name.startsWith('__pw');
   }
 
   static async dispatch(page: Page, payload: string, context: dom.FrameExecutionContext) {
@@ -896,6 +907,8 @@
       context.evaluate(deliverBindingResult, { name, seq, error }).catch(e => debugLogger.log('error', e));
     }
   }
+
+  readonly source: string;
 }
 
 export class InitScript {
@@ -905,14 +918,7 @@
 
   constructor(source: string, internal?: boolean, name?: string) {
     const guid = createGuid();
-    this.source = `(() => {
-      globalThis.__pwInitScripts = globalThis.__pwInitScripts || {};
-      const hasInitScript = globalThis.__pwInitScripts[${JSON.stringify(guid)}];
-      if (hasInitScript)
-        return;
-      globalThis.__pwInitScripts[${JSON.stringify(guid)}] = true;
-      ${source}
-    })();`;
+    this.source = `(() => { ${source} })();`;
     this.internal = !!internal;
     this.name = name;
   }
diff -ruN playwright/packages/playwright-core/src/server/pageBinding.ts patchright/packages/playwright-core/src/server/pageBinding.ts
---
+++
@@ -26,10 +26,11 @@
   serializedArgs?: SerializedValue[],
 };
 
-function addPageBinding(playwrightBinding: string, bindingName: string, needsHandle: boolean, utilityScriptSerializersFactory: typeof source, builtins: Builtins) {
+function addPageBinding(bindingName: string, needsHandle: boolean, utilityScriptSerializersFactory: typeof source, builtins: Builtins) {
   const { serializeAsCallArgument } = utilityScriptSerializersFactory(builtins);
   // eslint-disable-next-line no-restricted-globals
-  const binding = (globalThis as any)[playwrightBinding];
+  const binding = (globalThis as any)[bindingName];
+  if (!binding || binding.toString().startsWith("(...args) => {")) return
   // eslint-disable-next-line no-restricted-globals
   (globalThis as any)[bindingName] = (...args: any[]) => {
   // eslint-disable-next-line no-restricted-globals
@@ -66,7 +67,6 @@
     return promise;
   };
   // eslint-disable-next-line no-restricted-globals
-  (globalThis as any)[bindingName].__installed = true;
 }
 
 export function takeBindingHandle(arg: { name: string, seq: number }) {
@@ -87,6 +87,6 @@
   callbacks.delete(arg.seq);
 }
 
-export function createPageBindingScript(playwrightBinding: string, name: string, needsHandle: boolean) {
-  return `(${addPageBinding.toString()})(${JSON.stringify(playwrightBinding)}, ${JSON.stringify(name)}, ${needsHandle}, (${source}), (${builtins})())`;
+export function createPageBindingScript(name: string, needsHandle: boolean) {
+  return `(${addPageBinding.toString()})(${JSON.stringify(name)}, ${needsHandle}, (${source}), (${builtins})())`;
 }
diff -ruN playwright/packages/playwright-core/src/server/webkit/wkPage.ts patchright/packages/playwright-core/src/server/webkit/wkPage.ts
---
+++
@@ -768,7 +768,7 @@
     await this._updateBootstrapScript();
   }
 
-  async removeNonInternalInitScripts() {
+  async removeInitScripts() {
     await this._updateBootstrapScript();
   }
 
diff -ruN playwright/packages/playwright-core/src/utils/isomorphic/builtins.ts patchright/packages/playwright-core/src/utils/isomorphic/builtins.ts
---
+++
@@ -43,8 +43,7 @@
 // builtins instead of initializing them again.
 export function builtins(global?: typeof globalThis): Builtins {
   global = global ?? globalThis;
-  if (!(global as any)['__playwright_builtins__']) {
-    const builtins: Builtins = {
+    return {
       setTimeout: global.setTimeout?.bind(global),
       clearTimeout: global.clearTimeout?.bind(global),
       setInterval: global.setInterval?.bind(global),
@@ -58,11 +57,8 @@
       Intl: global.Intl,
       Date: global.Date,
       Map: global.Map,
-      Set: global.Set,
+      Set: global.Set
     };
-    Object.defineProperty(global, '__playwright_builtins__', { value: builtins, configurable: false, enumerable: false, writable: false });
-  }
-  return (global as any)['__playwright_builtins__'];
 }
 
 const instance = builtins();
diff -ruN playwright/packages/protocol/src/protocol.yml patchright/packages/protocol/src/protocol.yml
---
+++
@@ -1,17 +1,3 @@
-# Copyright (c) Microsoft Corporation.
-#
-# Licensed under the Apache License, Version 2.0 (the "License");
-# you may not use this file except in compliance with the License.
-# You may obtain a copy of the License at
-#
-# http://www.apache.org/licenses/LICENSE-2.0
-#
-# Unless required by applicable law or agreed to in writing, software
-# distributed under the License is distributed on an "AS IS" BASIS,
-# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-# See the License for the specific language governing permissions and
-# limitations under the License.
-
 StackFrame:
   type: object
   properties:
@@ -19,9 +5,6 @@
     line: number
     column: number
     function: string?
-
-# This object can be send with any rpc call in the "metadata" field.
-
 Metadata:
   type: object
   properties:
@@ -33,9 +16,7 @@
         column: number?
     apiName: string?
     internal: boolean?
-    # Test runner step id.
     stepId: string?
-
 ClientSideCallMetadata:
   type: object
   properties:
@@ -43,14 +24,11 @@
     stack:
       type: array?
       items: StackFrame
-
 Point:
   type: object
   properties:
     x: number
     y: number
-
-
 Rect:
   type: object
   properties:
@@ -58,11 +36,8 @@
     y: number
     width: number
     height: number
-
-
 SerializedValue:
   type: object
-  # Exactly one of the properties must be present.
   properties:
     n: number?
     b: boolean?
@@ -70,19 +45,15 @@
     v:
       type: enum?
       literals:
-      - null
-      - undefined
-      - NaN
-      - Infinity
-      - -Infinity
-      - "-0"
-    # String representation of the Date.
+        - null
+        - undefined
+        - NaN
+        - Infinity
+        - -Infinity
+        - "-0"
     d: string?
-    # String representation of the URL.
     u: string?
-    # String representation of BigInt.
     bi: string?
-    # Typed array.
     ta:
       type: object?
       properties:
@@ -90,25 +61,23 @@
         k:
           type: enum
           literals:
-          - i8
-          - ui8
-          - ui8c
-          - i16
-          - ui16
-          - i32
-          - ui32
-          - f32
-          - f64
-          - bi64
-          - bui64
-    # Serialized Error object.
+            - i8
+            - ui8
+            - ui8c
+            - i16
+            - ui16
+            - i32
+            - ui32
+            - f32
+            - f64
+            - bi64
+            - bui64
     e:
       type: object?
       properties:
         m: string
         n: string
         s: string
-    # Regular expression pattern and flags.
     r:
       type: object?
       properties:
@@ -117,7 +86,6 @@
     a:
       type: array?
       items: SerializedValue
-    # Object with keys and values.
     o:
       type: array?
       items:
@@ -125,15 +93,9 @@
         properties:
           k: string
           v: SerializedValue
-    # An index in the handles array from SerializedArgument.
     h: number?
-    # Index of the object in value-type for circular reference resolution.
     id: number?
-    # Ref to the object in value-type for circular reference resolution.
     ref: number?
-
-
-# Represents a value with handle references.
 SerializedArgument:
   type: object
   properties:
@@ -141,8 +103,6 @@
     handles:
       type: array
       items: Channel
-
-
 ExpectedTextValue:
   type: object
   properties:
@@ -152,8 +112,6 @@
     matchSubstring: boolean?
     ignoreCase: boolean?
     normalizeWhiteSpace: boolean?
-
-
 AXNode:
   type: object
   properties:
@@ -177,15 +135,15 @@
     checked:
       type: enum?
       literals:
-      - checked
-      - unchecked
-      - mixed
+        - checked
+        - unchecked
+        - mixed
     pressed:
       type: enum?
       literals:
-      - pressed
-      - released
-      - mixed
+        - pressed
+        - released
+        - mixed
     level: number?
     valuemin: number?
     valuemax: number?
@@ -196,8 +154,6 @@
     children:
       type: array?
       items: AXNode
-
-
 SetNetworkCookie:
   type: object
   properties:
@@ -212,11 +168,9 @@
     sameSite:
       type: enum?
       literals:
-      - Strict
-      - Lax
-      - None
-
-
+        - Strict
+        - Lax
+        - None
 NetworkCookie:
   type: object
   properties:
@@ -230,17 +184,14 @@
     sameSite:
       type: enum
       literals:
-      - Strict
-      - Lax
-      - None
-
-
+        - Strict
+        - Lax
+        - None
 NameValue:
   type: object
   properties:
     name: string
     value: string
-
 IndexedDBDatabase:
   type: object
   properties:
@@ -278,7 +229,6 @@
                   items: string
                 multiEntry: boolean
                 unique: boolean
-
 SetOriginStorage:
   type: object
   properties:
@@ -289,7 +239,6 @@
     indexedDB:
       type: array?
       items: IndexedDBDatabase
-
 OriginStorage:
   type: object
   properties:
@@ -300,7 +249,6 @@
     indexedDB:
       type: array?
       items: IndexedDBDatabase
-
 SerializedError:
   type: object
   properties:
@@ -311,8 +259,6 @@
         name: string
         stack: string?
     value: SerializedValue?
-
-
 RecordHarOptions:
   type: object
   properties:
@@ -320,19 +266,17 @@
     content:
       type: enum?
       literals:
-      - embed
-      - attach
-      - omit
+        - embed
+        - attach
+        - omit
     mode:
       type: enum?
       literals:
-      - full
-      - minimal
+        - full
+        - minimal
     urlGlob: string?
     urlRegexSource: string?
     urlRegexFlags: string?
-
-
 FormField:
   type: object
   properties:
@@ -344,15 +288,11 @@
         name: string
         mimeType: string?
         buffer: binary
-
 APIRequestContext:
   type: interface
-
   initializer:
     tracing: Tracing
-
   commands:
-
     fetch:
       parameters:
         url: string
@@ -379,13 +319,11 @@
         maxRetries: number?
       returns:
         response: APIResponse
-
     fetchResponseBody:
       parameters:
         fetchUid: string
       returns:
         binary: binary?
-
     fetchLog:
       parameters:
         fetchUid: string
@@ -393,7 +331,6 @@
         log:
           type: array
           items: string
-
     storageState:
       parameters:
         indexedDB: boolean?
@@ -404,16 +341,12 @@
         origins:
           type: array
           items: OriginStorage
-
     disposeAPIResponse:
       parameters:
         fetchUid: string
-
     dispose:
       parameters:
         reason: string?
-
-
 APIResponse:
   type: object
   properties:
@@ -424,16 +357,13 @@
     headers:
       type: array
       items: NameValue
-
-
 LifecycleEvent:
   type: enum
   literals:
-  - load
-  - domcontentloaded
-  - networkidle
-  - commit
-
+    - load
+    - domcontentloaded
+    - networkidle
+    - commit
 CommonScreenshotOptions:
   type: mixin
   properties:
@@ -441,18 +371,18 @@
     caret:
       type: enum?
       literals:
-      - hide
-      - initial
+        - hide
+        - initial
     animations:
       type: enum?
       literals:
-      - disabled
-      - allow
+        - disabled
+        - allow
     scale:
       type: enum?
       literals:
-      - css
-      - device
+        - css
+        - device
     mask:
       type: array?
       items:
@@ -462,7 +392,6 @@
           selector: string
     maskColor: string?
     style: string?
-
 LaunchOptions:
   type: mixin
   properties:
@@ -495,8 +424,6 @@
     tracesDir: string?
     chromiumSandbox: boolean?
     firefoxUserPrefs: json?
-
-
 ContextOptions:
   type: mixin
   properties:
@@ -549,42 +476,42 @@
         send:
           type: enum?
           literals:
-          - always
-          - unauthorized
+            - always
+            - unauthorized
     deviceScaleFactor: number?
     isMobile: boolean?
     hasTouch: boolean?
     colorScheme:
       type: enum?
       literals:
-      - dark
-      - light
-      - no-preference
-      - no-override
+        - dark
+        - light
+        - no-preference
+        - no-override
     reducedMotion:
       type: enum?
       literals:
-      - reduce
-      - no-preference
-      - no-override
+        - reduce
+        - no-preference
+        - no-override
     forcedColors:
       type: enum?
       literals:
-      - active
-      - none
-      - no-override
+        - active
+        - none
+        - no-override
     acceptDownloads:
       type: enum?
       literals:
-      - accept
-      - deny
-      - internal-browser-default
+        - accept
+        - deny
+        - internal-browser-default
     contrast:
       type: enum?
       literals:
-      - no-preference
-      - more
-      - no-override
+        - no-preference
+        - more
+        - no-override
     baseURL: string?
     recordVideo:
       type: object?
@@ -600,12 +527,10 @@
     serviceWorkers:
       type: enum?
       literals:
-      - allow
-      - block
-
+        - allow
+        - block
 LocalUtils:
   type: interface
-
   initializer:
     deviceDescriptors:
       type: array
@@ -633,12 +558,10 @@
               defaultBrowserType:
                 type: enum
                 literals:
-                - chromium
-                - firefox
-                - webkit
-
+                  - chromium
+                  - firefox
+                  - webkit
   commands:
-
     zip:
       parameters:
         zipFile: string
@@ -652,14 +575,12 @@
             - write
             - append
         includeSources: boolean
-
     harOpen:
       parameters:
         file: string
       returns:
         harId: string?
         error: string?
-
     harLookup:
       parameters:
         harId: string
@@ -674,10 +595,10 @@
         action:
           type: enum
           literals:
-          - error
-          - redirect
-          - fulfill
-          - noentry
+            - error
+            - redirect
+            - fulfill
+            - noentry
         message: string?
         redirectURL: string?
         status: number?
@@ -685,16 +606,13 @@
           type: array?
           items: NameValue
         body: binary?
-
     harClose:
       parameters:
         harId: string
-
     harUnzip:
       parameters:
         zipFile: string
         harFile: string
-
     connect:
       parameters:
         wsEndpoint: string
@@ -708,22 +626,18 @@
         headers:
           type: array
           items: NameValue
-
     tracingStarted:
       parameters:
         tracesDir: string?
         traceName: string
       returns:
         stacksId: string
-
     addStackToTracingNoReply:
       parameters:
         callData: ClientSideCallMetadata
-
     traceDiscarded:
       parameters:
         stacksId: string
-
     globToRegex:
       parameters:
         glob: string
@@ -731,27 +645,22 @@
         webSocketUrl: boolean?
       returns:
         regex: string
-
 Root:
   type: interface
-
   commands:
-
     initialize:
       parameters:
         sdkLanguage:
           type: enum
           literals:
-          - javascript
-          - python
-          - java
-          - csharp
+            - javascript
+            - python
+            - java
+            - csharp
       returns:
         playwright: Playwright
-
 Playwright:
   type: interface
-
   initializer:
     chromium: BrowserType
     firefox: BrowserType
@@ -762,13 +671,9 @@
     electron: Electron
     utils: LocalUtils?
     selectors: Selectors
-    # Only present when connecting remotely via BrowserType.connect() method.
     preLaunchedBrowser: Browser?
-    # Only present when connecting remotely via Android.connect() method.
     preConnectedAndroidDevice: AndroidDevice?
-    # Only present when socks proxy is supported.
     socksSupport: SocksSupport?
-
   commands:
     newRequest:
       parameters:
@@ -799,8 +704,8 @@
             send:
               type: enum?
               literals:
-              - always
-              - unauthorized
+                - always
+                - unauthorized
         proxy:
           type: object?
           properties:
@@ -819,10 +724,8 @@
               type: array?
               items: SetOriginStorage
         tracesDir: string?
-
       returns:
         request: APIRequestContext
-
 RecorderSource:
   type: object
   properties:
@@ -840,10 +743,8 @@
           type: string
     revealLine: number?
     group: string?
-
 DebugController:
   type: interface
-
   commands:
     initialize:
       parameters:
@@ -851,21 +752,17 @@
         sdkLanguage:
           type: enum
           literals:
-          - javascript
-          - python
-          - java
-          - csharp
-
+            - javascript
+            - python
+            - java
+            - csharp
     setReportStateChanged:
       parameters:
         enabled: boolean
-
-    resetForReuse:
-
+    resetForReuse: null
     navigate:
       parameters:
         url: string
-
     setRecorderMode:
       parameters:
         mode:
@@ -875,35 +772,26 @@
             - recording
             - none
         testIdAttributeName: string?
-
     highlight:
       parameters:
         selector: string?
         ariaTemplate: string?
-
-    hideHighlight:
-
-    resume:
-
-    kill:
-
-    closeAllBrowsers:
-
+    hideHighlight: null
+    resume: null
+    kill: null
+    closeAllBrowsers: null
   events:
     inspectRequested:
       parameters:
         selector: string
         locator: string
         ariaSnapshot: string
-
     setModeRequested:
       parameters:
         mode: string
-
     stateChanged:
       parameters:
         pageCount: number
-
     sourceChanged:
       parameters:
         text: string
@@ -912,87 +800,68 @@
         actions:
           type: array?
           items: string
-
     paused:
       parameters:
         paused: boolean
-
 SocksSupport:
   type: interface
-
   commands:
     socksConnected:
       parameters:
         uid: string
         host: string
         port: number
-
     socksFailed:
       parameters:
         uid: string
         errorCode: string
-
     socksData:
       parameters:
         uid: string
         data: binary
-
     socksError:
       parameters:
         uid: string
         error: string
-
     socksEnd:
       parameters:
         uid: string
-
   events:
     socksRequested:
       parameters:
         uid: string
         host: string
         port: number
-
     socksData:
       parameters:
         uid: string
         data: binary
-
     socksClosed:
       parameters:
         uid: string
-
 Selectors:
   type: interface
-
   commands:
-
     register:
       parameters:
         name: string
         source: string
         contentScript: boolean?
-
     setTestIdAttributeName:
       parameters:
         testIdAttributeName: string
-
 BrowserType:
   type: interface
-
   initializer:
     executablePath: string
     name: string
-
   commands:
-
     launch:
       parameters:
         $mixin: LaunchOptions
         slowMo: number?
       returns:
         browser: Browser
-
     launchPersistentContext:
       parameters:
         $mixin1: LaunchOptions
@@ -1001,7 +870,6 @@
         slowMo: number?
       returns:
         context: BrowserContext
-
     connectOverCDP:
       parameters:
         endpointURL: string
@@ -1013,26 +881,19 @@
       returns:
         browser: Browser
         defaultContext: BrowserContext?
-
 Browser:
   type: interface
-
   initializer:
     version: string
     name: string
-
   commands:
-
     close:
       parameters:
         reason: string?
-
-    killForTests:
-
+    killForTests: null
     defaultUserAgentForTest:
       returns:
         userAgent: string
-
     newContext:
       parameters:
         $mixin: ContextOptions
@@ -1054,7 +915,6 @@
               items: SetOriginStorage
       returns:
         context: BrowserContext
-
     newContextForReuse:
       parameters:
         $mixin: ContextOptions
@@ -1076,15 +936,12 @@
               items: SetOriginStorage
       returns:
         context: BrowserContext
-
     stopPendingOperations:
       parameters:
         reason: string
-
     newBrowserCDPSession:
       returns:
         session: CDPSession
-
     startTracing:
       parameters:
         page: Page?
@@ -1092,16 +949,11 @@
         categories:
           type: array?
           items: string
-
     stopTracing:
       returns:
         artifact: Artifact
-
-
   events:
-
-    close:
-
+    close: null
 ConsoleMessage:
   type: mixin
   properties:
@@ -1116,11 +968,8 @@
         url: string
         lineNumber: number
         columnNumber: number
-
-
 EventTarget:
   type: interface
-
   commands:
     waitForEventInfo:
       parameters:
@@ -1131,37 +980,30 @@
             phase:
               type: enum
               literals:
-              - before
-              - after
-              - log
+                - before
+                - after
+                - log
             event: string?
             message: string?
             error: string?
       flags:
         snapshot: true
-
 BrowserContext:
   type: interface
-
   extends: EventTarget
-
   initializer:
     isChromium: boolean
     requestContext: APIRequestContext
     tracing: Tracing
-
   commands:
-
     addCookies:
       parameters:
         cookies:
           type: array
           items: SetNetworkCookie
-
     addInitScript:
       parameters:
         source: string
-
     clearCookies:
       parameters:
         name: string?
@@ -1173,13 +1015,10 @@
         path: string?
         pathRegexSource: string?
         pathRegexFlags: string?
-
-    clearPermissions:
-
+    clearPermissions: null
     close:
       parameters:
         reason: string?
-
     cookies:
       parameters:
         urls:
@@ -1189,37 +1028,30 @@
         cookies:
           type: array
           items: NetworkCookie
-
     exposeBinding:
       parameters:
         name: string
         needsHandle: boolean?
-
     grantPermissions:
       parameters:
         permissions:
           type: array
           items: string
         origin: string?
-
     newPage:
       returns:
         page: Page
-
     setDefaultNavigationTimeoutNoReply:
       parameters:
         timeout: number?
-
     setDefaultTimeoutNoReply:
       parameters:
         timeout: number?
-
     setExtraHTTPHeaders:
       parameters:
         headers:
           type: array
           items: NameValue
-
     setGeolocation:
       parameters:
         geolocation:
@@ -1228,7 +1060,6 @@
             longitude: number
             latitude: number
             accuracy: number?
-
     setHTTPCredentials:
       parameters:
         httpCredentials:
@@ -1237,7 +1068,6 @@
             username: string
             password: string
             origin: string?
-
     setNetworkInterceptionPatterns:
       parameters:
         patterns:
@@ -1248,7 +1078,6 @@
               glob: string?
               regexSource: string?
               regexFlags: string?
-
     setWebSocketInterceptionPatterns:
       parameters:
         patterns:
@@ -1259,11 +1088,9 @@
               glob: string?
               regexSource: string?
               regexFlags: string?
-
     setOffline:
       parameters:
         offline: boolean
-
     storageState:
       parameters:
         indexedDB: boolean?
@@ -1274,19 +1101,17 @@
         origins:
           type: array
           items: OriginStorage
-
     pause:
-      experimental: True
-
+      experimental: true
     enableRecorder:
-      experimental: True
+      experimental: true
       parameters:
         language: string?
         mode:
           type: enum?
           literals:
-          - inspecting
-          - recording
+            - inspecting
+            - recording
         pauseOnNextStatement: boolean?
         testIdAttributeName: string?
         launchOptions: json?
@@ -1296,27 +1121,23 @@
         outputFile: string?
         handleSIGINT: boolean?
         omitCallTracking: boolean?
-
     newCDPSession:
       parameters:
         page: Page?
         frame: Frame?
       returns:
         session: CDPSession
-
     harStart:
       parameters:
         page: Page?
         options: RecordHarOptions
       returns:
         harId: string
-
     harExport:
       parameters:
         harId: string?
       returns:
         artifact: Artifact
-
     createTempFiles:
       parameters:
         rootDirName: string?
@@ -1332,127 +1153,100 @@
         writableStreams:
           type: array
           items: WritableStream
-
     updateSubscription:
       parameters:
         event:
           type: enum
           literals:
-          - console
-          - dialog
-          - request
-          - response
-          - requestFinished
-          - requestFailed
+            - console
+            - dialog
+            - request
+            - response
+            - requestFinished
+            - requestFailed
         enabled: boolean
-
     clockFastForward:
       parameters:
         ticksNumber: number?
         ticksString: string?
-
     clockInstall:
       parameters:
         timeNumber: number?
         timeString: string?
-
     clockPauseAt:
       parameters:
         timeNumber: number?
         timeString: string?
-
-    clockResume:
-
+    clockResume: null
     clockRunFor:
       parameters:
         ticksNumber: number?
         ticksString: string?
-
     clockSetFixedTime:
       parameters:
         timeNumber: number?
         timeString: string?
-
     clockSetSystemTime:
       parameters:
         timeNumber: number?
         timeString: string?
-
   events:
-
     bindingCall:
       parameters:
         binding: BindingCall
-
     console:
       parameters:
         $mixin: ConsoleMessage
         page: Page
-
-    close:
-
+    close: null
     dialog:
       parameters:
         dialog: Dialog
-
     page:
       parameters:
         page: Page
-
     pageError:
       parameters:
         error: SerializedError
         page: Page
-
     route:
       parameters:
         route: Route
-
     webSocketRoute:
       parameters:
         webSocketRoute: WebSocketRoute
-
     video:
       parameters:
         artifact: Artifact
-
     backgroundPage:
       parameters:
         page: Page
-
     serviceWorker:
       parameters:
         worker: Worker
-
     request:
       parameters:
         request: Request
         page: Page?
-
     requestFailed:
       parameters:
         request: Request
         failureText: string?
         responseEndTiming: number
         page: Page?
-
     requestFinished:
       parameters:
         request: Request
         response: Response?
         responseEndTiming: number
         page: Page?
-
     response:
       parameters:
         response: Response
         page: Page?
-
 Page:
   type: interface
-
   extends: EventTarget
-
   initializer:
     mainFrame: Frame
     viewportSize:
@@ -1462,67 +1256,59 @@
         height: number
     isClosed: boolean
     opener: Page?
-
   commands:
-
     setDefaultNavigationTimeoutNoReply:
       parameters:
         timeout: number?
-
     setDefaultTimeoutNoReply:
       parameters:
         timeout: number?
-
     addInitScript:
       parameters:
         source: string
-
     close:
       parameters:
         runBeforeUnload: boolean?
         reason: string?
-
     emulateMedia:
       parameters:
         media:
           type: enum?
           literals:
-          - screen
-          - print
-          - no-override
+            - screen
+            - print
+            - no-override
         colorScheme:
           type: enum?
           literals:
-          - dark
-          - light
-          - no-preference
-          - no-override
+            - dark
+            - light
+            - no-preference
+            - no-override
         reducedMotion:
           type: enum?
           literals:
-          - reduce
-          - no-preference
-          - no-override
+            - reduce
+            - no-preference
+            - no-override
         forcedColors:
           type: enum?
           literals:
-          - active
-          - none
-          - no-override
+            - active
+            - none
+            - no-override
         contrast:
           type: enum?
           literals:
-          - no-preference
-          - more
-          - no-override
+            - no-preference
+            - more
+            - no-override
       flags:
         snapshot: true
-
     exposeBinding:
       parameters:
         name: string
         needsHandle: boolean?
-
     goBack:
       parameters:
         timeout: number?
@@ -1532,7 +1318,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     goForward:
       parameters:
         timeout: number?
@@ -1542,25 +1327,20 @@
       flags:
         slowMo: true
         snapshot: true
-
-    requestGC:
-
+    requestGC: null
     registerLocatorHandler:
       parameters:
         selector: string
         noWaitAfter: boolean?
       returns:
         uid: number
-
     resolveLocatorHandlerNoReply:
       parameters:
         uid: number
         remove: boolean?
-
     unregisterLocatorHandler:
       parameters:
         uid: number
-
     reload:
       parameters:
         timeout: number?
@@ -1570,7 +1350,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     expectScreenshot:
       parameters:
         expected: binary?
@@ -1599,15 +1378,14 @@
           items: string
       flags:
         snapshot: true
-
     screenshot:
       parameters:
         timeout: number?
         type:
           type: enum?
           literals:
-          - png
-          - jpeg
+            - png
+            - jpeg
         quality: number?
         fullPage: boolean?
         clip: Rect?
@@ -1616,13 +1394,11 @@
         binary: binary
       flags:
         snapshot: true
-
     setExtraHTTPHeaders:
       parameters:
         headers:
           type: array
           items: NameValue
-
     setNetworkInterceptionPatterns:
       parameters:
         patterns:
@@ -1633,7 +1409,6 @@
               glob: string?
               regexSource: string?
               regexFlags: string?
-
     setWebSocketInterceptionPatterns:
       parameters:
         patterns:
@@ -1644,7 +1419,6 @@
               glob: string?
               regexSource: string?
               regexFlags: string?
-
     setViewportSize:
       parameters:
         viewportSize:
@@ -1654,28 +1428,24 @@
             height: number
       flags:
         snapshot: true
-
     keyboardDown:
       parameters:
         key: string
       flags:
         slowMo: true
         snapshot: true
-
     keyboardUp:
       parameters:
         key: string
       flags:
         slowMo: true
         snapshot: true
-
     keyboardInsertText:
       parameters:
         text: string
       flags:
         slowMo: true
         snapshot: true
-
     keyboardType:
       parameters:
         text: string
@@ -1683,7 +1453,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     keyboardPress:
       parameters:
         key: string
@@ -1691,7 +1460,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     mouseMove:
       parameters:
         x: number
@@ -1700,33 +1468,30 @@
       flags:
         slowMo: true
         snapshot: true
-
     mouseDown:
       parameters:
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         clickCount: number?
       flags:
         slowMo: true
         snapshot: true
-
     mouseUp:
       parameters:
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         clickCount: number?
       flags:
         slowMo: true
         snapshot: true
-
     mouseClick:
       parameters:
         x: number
@@ -1735,14 +1500,13 @@
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         clickCount: number?
       flags:
         slowMo: true
         snapshot: true
-
     mouseWheel:
       parameters:
         deltaX: number
@@ -1750,7 +1514,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     touchscreenTap:
       parameters:
         x: number
@@ -1758,7 +1521,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     accessibilitySnapshot:
       parameters:
         interestingOnly: boolean?
@@ -1767,7 +1529,6 @@
         rootAXNode: AXNode?
       flags:
         snapshot: true
-
     pdf:
       parameters:
         scale: number?
@@ -1792,12 +1553,10 @@
         outline: boolean?
       returns:
         pdf: binary
-
     startJSCoverage:
       parameters:
         resetOnNavigation: boolean?
         reportAnonymousScripts: boolean?
-
     stopJSCoverage:
       returns:
         entries:
@@ -1823,11 +1582,9 @@
                           startOffset: number
                           endOffset: number
                           count: number
-
     startCSSCoverage:
       parameters:
         resetOnNavigation: boolean?
-
     stopCSSCoverage:
       returns:
         entries:
@@ -1844,81 +1601,61 @@
                   properties:
                     start: number
                     end: number
-
-    bringToFront:
-
+    bringToFront: null
     updateSubscription:
       parameters:
         event:
           type: enum
           literals:
-          - console
-          - dialog
-          - fileChooser
-          - request
-          - response
-          - requestFinished
-          - requestFailed
+            - console
+            - dialog
+            - fileChooser
+            - request
+            - response
+            - requestFinished
+            - requestFailed
         enabled: boolean
-
   events:
-
     bindingCall:
       parameters:
         binding: BindingCall
-
-    close:
-
-    crash:
-
+    close: null
+    crash: null
     download:
       parameters:
         url: string
         suggestedFilename: string
         artifact: Artifact
-
     fileChooser:
       parameters:
         element: ElementHandle
         isMultiple: boolean
-
     frameAttached:
       parameters:
         frame: Frame
-
     frameDetached:
       parameters:
         frame: Frame
-
     locatorHandlerTriggered:
       parameters:
         uid: number
-
     route:
       parameters:
         route: Route
-
     webSocketRoute:
       parameters:
         webSocketRoute: WebSocketRoute
-
     video:
       parameters:
         artifact: Artifact
-
     webSocket:
       parameters:
         webSocket: WebSocket
-
     worker:
       parameters:
         worker: Worker
-
-
-
 Frame:
   type: interface
-
   initializer:
     url: string
     name: string
@@ -1926,9 +1663,7 @@
     loadStates:
       type: array
       items: LifecycleEvent
-
   commands:
-
     evalOnSelector:
       parameters:
         selector: string
@@ -1940,7 +1675,6 @@
         value: SerializedValue
       flags:
         snapshot: true
-
     evalOnSelectorAll:
       parameters:
         selector: string
@@ -1951,7 +1685,6 @@
         value: SerializedValue
       flags:
         snapshot: true
-
     addScriptTag:
       parameters:
         url: string?
@@ -1961,7 +1694,6 @@
         element: ElementHandle
       flags:
         snapshot: true
-
     addStyleTag:
       parameters:
         url: string?
@@ -1970,7 +1702,6 @@
         element: ElementHandle
       flags:
         snapshot: true
-
     ariaSnapshot:
       parameters:
         selector: string
@@ -1978,14 +1709,13 @@
         mode:
           type: enum?
           literals:
-          - raw
-          - regex
+            - raw
+            - regex
         timeout: number?
       returns:
         snapshot: string
       flags:
         snapshot: true
-
     blur:
       parameters:
         selector: string
@@ -1994,7 +1724,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     check:
       parameters:
         selector: string
@@ -2007,7 +1736,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     click:
       parameters:
         selector: string
@@ -2019,19 +1747,19 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         delay: number?
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         clickCount: number?
         timeout: number?
         trial: boolean?
@@ -2039,13 +1767,11 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     content:
       returns:
         value: string
       flags:
         snapshot: true
-
     dragAndDrop:
       parameters:
         source: string
@@ -2060,7 +1786,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     dblclick:
       parameters:
         selector: string
@@ -2071,26 +1796,25 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         delay: number?
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         timeout: number?
         trial: boolean?
       flags:
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     dispatchEvent:
       parameters:
         selector: string
@@ -2101,27 +1825,26 @@
       flags:
         slowMo: true
         snapshot: true
-
     evaluateExpression:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         value: SerializedValue
       flags:
         snapshot: true
-
     evaluateExpressionHandle:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         handle: JSHandle
       flags:
         snapshot: true
-
     fill:
       parameters:
         selector: string
@@ -2133,7 +1856,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     focus:
       parameters:
         selector: string
@@ -2142,15 +1864,12 @@
       flags:
         slowMo: true
         snapshot: true
-
     frameElement:
       returns:
         element: ElementHandle
-
     highlight:
       parameters:
         selector: string
-
     getAttribute:
       parameters:
         selector: string
@@ -2161,7 +1880,6 @@
         value: string?
       flags:
         snapshot: true
-
     goto:
       parameters:
         url: string
@@ -2173,7 +1891,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     hover:
       parameters:
         selector: string
@@ -2184,11 +1901,11 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         timeout: number?
         trial: boolean?
@@ -2196,7 +1913,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     innerHTML:
       parameters:
         selector: string
@@ -2206,7 +1922,6 @@
         value: string
       flags:
         snapshot: true
-
     innerText:
       parameters:
         selector: string
@@ -2216,7 +1931,6 @@
         value: string
       flags:
         snapshot: true
-
     inputValue:
       parameters:
         selector: string
@@ -2226,7 +1940,6 @@
         value: string
       flags:
         snapshot: true
-
     isChecked:
       parameters:
         selector: string
@@ -2236,7 +1949,6 @@
         value: boolean
       flags:
         snapshot: true
-
     isDisabled:
       parameters:
         selector: string
@@ -2246,7 +1958,6 @@
         value: boolean
       flags:
         snapshot: true
-
     isEnabled:
       parameters:
         selector: string
@@ -2256,7 +1967,6 @@
         value: boolean
       flags:
         snapshot: true
-
     isHidden:
       parameters:
         selector: string
@@ -2265,7 +1975,6 @@
         value: boolean
       flags:
         snapshot: true
-
     isVisible:
       parameters:
         selector: string
@@ -2274,7 +1983,6 @@
         value: boolean
       flags:
         snapshot: true
-
     isEditable:
       parameters:
         selector: string
@@ -2284,7 +1992,6 @@
         value: boolean
       flags:
         snapshot: true
-
     press:
       parameters:
         selector: string
@@ -2297,7 +2004,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     querySelector:
       parameters:
         selector: string
@@ -2306,7 +2012,6 @@
         element: ElementHandle?
       flags:
         snapshot: true
-
     querySelectorAll:
       parameters:
         selector: string
@@ -2316,7 +2021,6 @@
           items: ElementHandle
       flags:
         snapshot: true
-
     queryCount:
       parameters:
         selector: string
@@ -2324,7 +2028,6 @@
         value: number
       flags:
         snapshot: true
-
     selectOption:
       parameters:
         selector: string
@@ -2351,7 +2054,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     setContent:
       parameters:
         html: string
@@ -2359,12 +2061,10 @@
         waitUntil: LifecycleEvent?
       flags:
         snapshot: true
-
     setInputFiles:
       parameters:
         selector: string
         strict: boolean?
-        # Only one of payloads, localPaths and streams should be present.
         payloads:
           type: array?
           items:
@@ -2386,7 +2086,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     tap:
       parameters:
         selector: string
@@ -2397,11 +2096,11 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         timeout: number?
         trial: boolean?
@@ -2409,7 +2108,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     textContent:
       parameters:
         selector: string
@@ -2419,11 +2117,9 @@
         value: string?
       flags:
         snapshot: true
-
     title:
       returns:
         value: string
-
     type:
       parameters:
         selector: string
@@ -2435,7 +2131,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     uncheck:
       parameters:
         selector: string
@@ -2448,26 +2143,22 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     waitForTimeout:
       parameters:
         timeout: number
       flags:
         snapshot: true
-
     waitForFunction:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
         timeout: number?
-        # When present, polls on interval. Otherwise, polls on raf.
         pollingInterval: number?
       returns:
         handle: JSHandle
       flags:
         snapshot: true
-
     waitForSelector:
       parameters:
         selector: string
@@ -2476,16 +2167,15 @@
         state:
           type: enum?
           literals:
-          - attached
-          - detached
-          - visible
-          - hidden
+            - attached
+            - detached
+            - visible
+            - hidden
         omitReturnValue: boolean?
       returns:
         element: ElementHandle?
       flags:
         snapshot: true
-
     expect:
       parameters:
         selector: string
@@ -2508,14 +2198,11 @@
           items: string
       flags:
         snapshot: true
-
   events:
-
     loadstate:
       parameters:
         add: LifecycleEvent?
         remove: LifecycleEvent?
-
     navigated:
       parameters:
         url: string
@@ -2525,68 +2212,55 @@
           properties:
             request: Request?
         error: string?
-
-
-
 Worker:
   type: interface
-
   initializer:
     url: string
-
   commands:
-
     evaluateExpression:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         value: SerializedValue
-
     evaluateExpressionHandle:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         handle: JSHandle
-
   events:
-
-    close:
-
-
+    close: null
 JSHandle:
   type: interface
-
   initializer:
     preview: string
-
   commands:
-
-    dispose:
-
+    dispose: null
     evaluateExpression:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         value: SerializedValue
       flags:
         snapshot: true
-
     evaluateExpressionHandle:
       parameters:
         expression: string
         isFunction: boolean?
         arg: SerializedArgument
+        isolatedContext: boolean?
       returns:
         handle: JSHandle
       flags:
         snapshot: true
-
     getPropertyList:
       returns:
         properties:
@@ -2596,32 +2270,22 @@
             properties:
               name: string
               value: JSHandle
-
     getProperty:
       parameters:
         name: string
       returns:
         handle: JSHandle
-
     jsonValue:
       returns:
         value: SerializedValue
-
   events:
-
     previewUpdated:
       parameters:
         preview: string
-
-
-
 ElementHandle:
   type: interface
-
   extends: JSHandle
-
   commands:
-
     evalOnSelector:
       parameters:
         selector: string
@@ -2633,7 +2297,6 @@
         value: SerializedValue
       flags:
         snapshot: true
-
     evalOnSelectorAll:
       parameters:
         selector: string
@@ -2644,13 +2307,11 @@
         value: SerializedValue
       flags:
         snapshot: true
-
     boundingBox:
       returns:
         value: Rect?
       flags:
         snapshot: true
-
     check:
       parameters:
         force: boolean?
@@ -2661,7 +2322,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     click:
       parameters:
         force: boolean?
@@ -2671,19 +2331,19 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         delay: number?
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         clickCount: number?
         timeout: number?
         trial: boolean?
@@ -2691,13 +2351,11 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     contentFrame:
       returns:
         frame: Frame?
       flags:
         snapshot: true
-
     dblclick:
       parameters:
         force: boolean?
@@ -2706,26 +2364,25 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         delay: number?
         button:
           type: enum?
           literals:
-          - left
-          - right
-          - middle
+            - left
+            - right
+            - middle
         timeout: number?
         trial: boolean?
       flags:
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     dispatchEvent:
       parameters:
         type: string
@@ -2733,7 +2390,6 @@
       flags:
         slowMo: true
         snapshot: true
-
     fill:
       parameters:
         value: string
@@ -2743,22 +2399,18 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     focus:
       flags:
         slowMo: true
         snapshot: true
-
     generateLocatorString:
       returns:
         value: string?
-
     getAttribute:
       parameters:
         name: string
       returns:
         value: string?
-
     hover:
       parameters:
         force: boolean?
@@ -2767,11 +2419,11 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         timeout: number?
         trial: boolean?
@@ -2779,65 +2431,54 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     innerHTML:
       returns:
         value: string
       flags:
         snapshot: true
-
     innerText:
       returns:
         value: string
       flags:
         snapshot: true
-
     inputValue:
       returns:
         value: string
       flags:
         snapshot: true
-
     isChecked:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     isDisabled:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     isEditable:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     isEnabled:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     isHidden:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     isVisible:
       returns:
         value: boolean
       flags:
         snapshot: true
-
     ownerFrame:
       returns:
         frame: Frame?
-
     press:
       parameters:
         key: string
@@ -2848,7 +2489,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     querySelector:
       parameters:
         selector: string
@@ -2857,7 +2497,6 @@
         element: ElementHandle?
       flags:
         snapshot: true
-
     querySelectorAll:
       parameters:
         selector: string
@@ -2867,29 +2506,26 @@
           items: ElementHandle
       flags:
         snapshot: true
-
     screenshot:
       parameters:
         timeout: number?
         type:
           type: enum?
           literals:
-          - png
-          - jpeg
+            - png
+            - jpeg
         quality: number?
         $mixin: CommonScreenshotOptions
       returns:
         binary: binary
       flags:
         snapshot: true
-
     scrollIntoViewIfNeeded:
       parameters:
         timeout: number?
       flags:
         slowMo: true
         snapshot: true
-
     selectOption:
       parameters:
         elements:
@@ -2914,7 +2550,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     selectText:
       parameters:
         force: boolean?
@@ -2922,10 +2557,8 @@
       flags:
         slowMo: true
         snapshot: true
-
     setInputFiles:
       parameters:
-        # Only one of payloads, localPaths and streams should be present.
         payloads:
           type: array?
           items:
@@ -2947,7 +2580,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     tap:
       parameters:
         force: boolean?
@@ -2956,11 +2588,11 @@
           items:
             type: enum
             literals:
-            - Alt
-            - Control
-            - ControlOrMeta
-            - Meta
-            - Shift
+              - Alt
+              - Control
+              - ControlOrMeta
+              - Meta
+              - Shift
         position: Point?
         timeout: number?
         trial: boolean?
@@ -2968,13 +2600,11 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     textContent:
       returns:
         value: string?
       flags:
         snapshot: true
-
     type:
       parameters:
         text: string
@@ -2984,7 +2614,6 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     uncheck:
       parameters:
         force: boolean?
@@ -2995,22 +2624,20 @@
         slowMo: true
         snapshot: true
         pausesBeforeInput: true
-
     waitForElementState:
       parameters:
         state:
           type: enum
           literals:
-          - visible
-          - hidden
-          - stable
-          - enabled
-          - disabled
-          - editable
+            - visible
+            - hidden
+            - stable
+            - enabled
+            - disabled
+            - editable
         timeout: number?
       flags:
         snapshot: true
-
     waitForSelector:
       parameters:
         selector: string
@@ -3019,19 +2646,16 @@
         state:
           type: enum?
           literals:
-          - attached
-          - detached
-          - visible
-          - hidden
+            - attached
+            - detached
+            - visible
+            - hidden
       returns:
         element: ElementHandle?
       flags:
         snapshot: true
-
-
 Request:
   type: interface
-
   initializer:
     frame: Frame?
     serviceWorker: Worker?
@@ -3044,36 +2668,26 @@
       items: NameValue
     isNavigationRequest: boolean
     redirectedFrom: Request?
-
   commands:
-
     response:
       returns:
         response: Response?
-
     rawRequestHeaders:
       returns:
         headers:
           type: array
           items: NameValue
-
-
 Route:
   type: interface
-
   initializer:
     request: Request
-
   commands:
-
     redirectNavigationRequest:
       parameters:
         url: string
-
     abort:
       parameters:
         errorCode: string?
-
     continue:
       parameters:
         url: string?
@@ -3083,10 +2697,8 @@
           items: NameValue
         postData: binary?
         isFallback: boolean
-
     fulfill:
       parameters:
-        # default is 200
         status: number?
         headers:
           type: array?
@@ -3094,67 +2706,50 @@
         body: string?
         isBase64: boolean?
         fetchResponseUid: string?
-
-
 WebSocketRoute:
   type: interface
-
   initializer:
     url: string
-
   commands:
-
-    connect:
-
-    ensureOpened:
-
+    connect: null
+    ensureOpened: null
     sendToPage:
       parameters:
         message: string
         isBase64: boolean
-
     sendToServer:
       parameters:
         message: string
         isBase64: boolean
-
     closePage:
       parameters:
         code: number?
         reason: string?
         wasClean: boolean
-
     closeServer:
       parameters:
         code: number?
         reason: string?
         wasClean: boolean
-
   events:
-
     messageFromPage:
       parameters:
         message: string
         isBase64: boolean
-
     messageFromServer:
       parameters:
         message: string
         isBase64: boolean
-
     closePage:
       parameters:
         code: number?
         reason: string?
         wasClean: boolean
-
     closeServer:
       parameters:
         code: number?
         reason: string?
         wasClean: boolean
-
-
 ResourceTiming:
   type: object
   properties:
@@ -3166,10 +2761,8 @@
     connectEnd: number
     requestStart: number
     responseStart: number
-
 Response:
   type: interface
-
   initializer:
     request: Request
     url: string
@@ -3180,33 +2773,24 @@
       items: NameValue
     timing: ResourceTiming
     fromServiceWorker: boolean
-
-
   commands:
-
     body:
       returns:
         binary: binary
-
     securityDetails:
       returns:
         value: SecurityDetails?
-
     serverAddr:
       returns:
         value: RemoteAddr?
-
     rawResponseHeaders:
       returns:
         headers:
           type: array
           items: NameValue
-
     sizes:
       returns:
         sizes: RequestSizes
-
-
 SecurityDetails:
   type: object
   properties:
@@ -3215,7 +2799,6 @@
     subjectName: string?
     validFrom: number?
     validTo: number?
-
 RequestSizes:
   type: object
   properties:
@@ -3223,46 +2806,32 @@
     requestHeadersSize: number
     responseBodySize: number
     responseHeadersSize: number
-
-
 RemoteAddr:
   type: object
   properties:
     ipAddress: string
     port: number
-
-
 WebSocket:
   type: interface
-
   extends: EventTarget
-
   initializer:
     url: string
-
   events:
-    open:
-
+    open: null
     frameSent:
       parameters:
         opcode: number
         data: string
-
     frameReceived:
       parameters:
         opcode: number
         data: string
-
     socketError:
       parameters:
         error: string
-
-    close:
-
-
+    close: null
 BindingCall:
   type: interface
-
   initializer:
     frame: Frame
     name: string
@@ -3270,56 +2839,40 @@
       type: array?
       items: SerializedValue
     handle: JSHandle?
-
   commands:
-
     reject:
       parameters:
         error: SerializedError
-
     resolve:
       parameters:
         result: SerializedArgument
-
-
-
 Dialog:
   type: interface
-
   initializer:
     page: Page?
     type: string
     message: string
     defaultValue: string
-
   commands:
-
     accept:
       parameters:
         promptText: string?
-
-    dismiss:
-
-
+    dismiss: null
 Tracing:
   type: interface
-
   commands:
-
     tracingStart:
       parameters:
         name: string?
         snapshots: boolean?
         screenshots: boolean?
         live: boolean?
-
     tracingStartChunk:
       parameters:
         name: string?
         title: string?
       returns:
         traceName: string
-
     tracingGroup:
       parameters:
         name: string
@@ -3329,117 +2882,77 @@
             file: string
             line: number?
             column: number?
-
-    tracingGroupEnd:
-
+    tracingGroupEnd: null
     tracingStopChunk:
       parameters:
         mode:
           type: enum
           literals:
-          - archive
-          - discard
-          - entries
+            - archive
+            - discard
+            - entries
       returns:
-        # The artifact may be missing if the browser closes while tracing is being stopped.
-        # Or it can be missing if client-side compression is taking place.
         artifact: Artifact?
-        # For local mode, these are all entries.
         entries:
           type: array?
           items: NameValue
-
-    tracingStop:
-
-
+    tracingStop: null
 Artifact:
   type: interface
-
   initializer:
     absolutePath: string
-
   commands:
-
     pathAfterFinished:
       returns:
         value: string
-
-    # Blocks path/failure/delete/context.close until saved to the local |path|.
     saveAs:
       parameters:
         path: string
-
-    # Blocks path/failure/delete/context.close until the stream is closed.
     saveAsStream:
       returns:
         stream: Stream
-
     failure:
       returns:
         error: string?
-
     stream:
       returns:
         stream: Stream
-
-    cancel:
-
-    delete:
-
-
+    cancel: null
+    delete: null
 Stream:
   type: interface
-
   commands:
-
     read:
       parameters:
         size: number?
       returns:
         binary: binary
-
-    close:
-
-
+    close: null
 WritableStream:
   type: interface
-
   commands:
-
     write:
       parameters:
         binary: binary
-
-    close:
-
-
+    close: null
 CDPSession:
   type: interface
-
   commands:
-
     send:
       parameters:
         method: string
         params: json?
       returns:
         result: json
-
-    detach:
-
+    detach: null
   events:
-
     event:
       parameters:
         method: string
         params: json?
-
-
 Electron:
   type: interface
-
   commands:
-
     launch:
       parameters:
         executablePath: string?
@@ -3454,17 +2967,17 @@
         acceptDownloads:
           type: enum?
           literals:
-          - accept
-          - deny
-          - internal-browser-default
+            - accept
+            - deny
+            - internal-browser-default
         bypassCSP: boolean?
         colorScheme:
           type: enum?
           literals:
-          - dark
-          - light
-          - no-preference
-          - no-override
+            - dark
+            - light
+            - no-preference
+            - no-override
         extraHTTPHeaders:
           type: array?
           items: NameValue
@@ -3496,27 +3009,19 @@
         strictSelectors: boolean?
         timezoneId: string?
         tracesDir: string?
-
       returns:
         electronApplication: ElectronApplication
-
-
 ElectronApplication:
   type: interface
-
   extends: EventTarget
-
   initializer:
     context: BrowserContext
-
   commands:
-
     browserWindow:
       parameters:
         page: Page
       returns:
         handle: JSHandle
-
     evaluateExpression:
       parameters:
         expression: string
@@ -3524,7 +3029,6 @@
         arg: SerializedArgument
       returns:
         value: SerializedValue
-
     evaluateExpressionHandle:
       parameters:
         expression: string
@@ -3532,26 +3036,21 @@
         arg: SerializedArgument
       returns:
         handle: JSHandle
-
     updateSubscription:
       parameters:
         event:
           type: enum
           literals:
-          - console
+            - console
         enabled: boolean
-
   events:
-    close:
+    close: null
     console:
       parameters:
         $mixin: ConsoleMessage
-
 Android:
   type: interface
-
   commands:
-
     devices:
       parameters:
         host: string?
@@ -3561,36 +3060,27 @@
         devices:
           type: array
           items: AndroidDevice
-
     setDefaultTimeoutNoReply:
       parameters:
         timeout: number
-
 AndroidSocket:
   type: interface
-
   commands:
     write:
       parameters:
         data: binary
-
-    close:
-
+    close: null
   events:
     data:
       parameters:
         data: binary
-    close:
-
+    close: null
 AndroidDevice:
   type: interface
-
   extends: EventTarget
-
   initializer:
     model: string
     serial: string
-
   commands:
     wait:
       parameters:
@@ -3598,123 +3088,106 @@
         state:
           type: enum?
           literals:
-          - gone
+            - gone
         timeout: number?
-
     fill:
       parameters:
         selector: AndroidSelector
         text: string
         timeout: number?
-
     tap:
       parameters:
         selector: AndroidSelector
         duration: number?
         timeout: number?
-
     drag:
       parameters:
         selector: AndroidSelector
         dest: Point
         speed: number?
         timeout: number?
-
     fling:
       parameters:
         selector: AndroidSelector
         direction:
           type: enum
           literals:
-          - up
-          - down
-          - left
-          - right
+            - up
+            - down
+            - left
+            - right
         speed: number?
         timeout: number?
-
     longTap:
       parameters:
         selector: AndroidSelector
         timeout: number?
-
     pinchClose:
       parameters:
         selector: AndroidSelector
         percent: number
         speed: number?
         timeout: number?
-
     pinchOpen:
       parameters:
         selector: AndroidSelector
         percent: number
         speed: number?
         timeout: number?
-
     scroll:
       parameters:
         selector: AndroidSelector
         direction:
           type: enum
           literals:
-          - up
-          - down
-          - left
-          - right
+            - up
+            - down
+            - left
+            - right
         percent: number
         speed: number?
         timeout: number?
-
     swipe:
       parameters:
         selector: AndroidSelector
         direction:
           type: enum
           literals:
-          - up
-          - down
-          - left
-          - right
+            - up
+            - down
+            - left
+            - right
         percent: number
         speed: number?
         timeout: number?
-
     info:
       parameters:
         selector: AndroidSelector
       returns:
         info: AndroidElementInfo
-
     screenshot:
       returns:
         binary: binary
-
     inputType:
       parameters:
         text: string
-
     inputPress:
       parameters:
         key: string
-
     inputTap:
       parameters:
         point: Point
-
     inputSwipe:
       parameters:
         segments:
           type: array
           items: Point
         steps: number
-
     inputDrag:
       parameters:
         from: Point
         to: Point
         steps: number
-
     launchBrowser:
       parameters:
         $mixin: ContextOptions
@@ -3729,67 +3202,52 @@
             bypass: string?
             username: string?
             password: string?
-
       returns:
         context: BrowserContext
-
     open:
       parameters:
         command: string
       returns:
         socket: AndroidSocket
-
     shell:
       parameters:
         command: string
       returns:
         result: binary
-
     installApk:
       parameters:
         file: binary
         args:
           type: array?
           items: string
-
     push:
       parameters:
         file: binary
         path: string
         mode: number?
-
     setDefaultTimeoutNoReply:
       parameters:
         timeout: number
-
     connectToWebView:
       parameters:
         socketName: string
       returns:
         context: BrowserContext
-
-    close:
-
+    close: null
   events:
-    close:
-
+    close: null
     webViewAdded:
       parameters:
         webView: AndroidWebView
-
     webViewRemoved:
       parameters:
         socketName: string
-
-
 AndroidWebView:
   type: object
   properties:
     pid: number
     pkg: string
     socketName: string
-
-
 AndroidSelector:
   type: object
   properties:
@@ -3817,8 +3275,6 @@
     scrollable: boolean?
     selected: boolean?
     text: string?
-
-
 AndroidElementInfo:
   type: object
   properties:
@@ -3840,24 +3296,17 @@
     longClickable: boolean
     scrollable: boolean
     selected: boolean
-
-
 JsonPipe:
   type: interface
-
   commands:
     send:
       parameters:
         message: json
-
-    close:
-
+    close: null
   events:
-
     message:
       parameters:
         message: json
-
     closed:
       parameters:
         reason: string?
