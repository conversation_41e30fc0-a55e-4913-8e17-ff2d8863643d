using Microsoft.Playwright;
using System.Runtime.InteropServices;

public class SimpleP<PERSON>rightExample
{
  // Windows API function to check if a key is pressed globally
  [DllImport( "user32.dll" )]
  private static extern short GetAsyncKeyState( int vKey );

  // Virtual key codes for escape keys
  private const int VK_ESCAPE = 0x1B;
  private const int VK_LCONTROL = 0xA2;
  private const int VK_RCONTROL = 0xA3;
  public static async Task Main()
  {
    Console.WriteLine( "Starting Simple Patchright C# Example..." );

    try
    {
      // Install Playwright if needed
      Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });

      // Create Playwright instance
      using var playwright = await Playwright.CreateAsync();

      // Launch browser with stealth args
      var browser = await playwright.Chromium.LaunchAsync( new BrowserTypeLaunchOptions
      {
        Headless = false,
        Args = new[]
                {
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--no-service-autorun",
                    "--no-default-browser-check",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-component-extensions-with-background-pages"
                }
      } );

      var page = await browser.NewPageAsync(new BrowserNewPageOptions { UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36" });

      // var browser = await playwright.Chromium.LaunchPersistentContextAsync( "", new BrowserTypeLaunchPersistentContextOptions
      //                                                                         {
      //                                                                             Headless = false,
      //                                                                             Args = new[]
      //                                                                                     {
      //                                                                                         "--disable-blink-features=AutomationControlled",
      //                                                                                         "--no-first-run",
      //                                                                                         "--password-store=basic",
      //                                                                                         "--use-mock-keychain"
      //                                                                                     }
      //                                                                         }
      //                                                                     );

      // var page = await browser.NewPageAsync();

      // Set up console logging to capture browser/platform detection attempts
      page.Console += (_, e) =>
      {
          Console.WriteLine($"[BROWSER CONSOLE] {e.Type}: {e.Text}");
      };

      // Add stealth script before navigation
      await page.AddInitScriptAsync(@"
                // Set up debug logging function
                window.playwrightDebug = function(message) {
                    console.log('[PLAYWRIGHT DEBUG] ' + message);
                };
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                    configurable: false
                });

                Object.defineProperty(window, 'outerWidth', { 
                    get: () => window.innerWidth
                });

                // Spoof plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => { 'plugins': 
                                  [
                                    'PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chrome PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chromium PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Microsoft Edge PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'WebKit built-in PDF::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format'
                                  ]
                                },
                    configurable: true
                });

                // Fix video codec detection to match real browser behavior
                const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                HTMLVideoElement.prototype.canPlayType = function(type) {
                    // Return proper format like real browsers do - be more specific with H.264
                    if (type.includes('avc1') || type.includes('h264') || type.includes('H.264')) {
                        return 'probably';
                    }
                    if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('theora')) {
                        return 'maybe';
                    }
                    if (type.includes('mp4') && !type.includes('codecs')) {
                        return 'probably';
                    }

                    // Fallback to original for other types
                    return originalCanPlayType.call(this, type);
                };

                // Also fix HTMLAudioElement for audio codec detection
                const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
                HTMLAudioElement.prototype.canPlayType = function(type) {
                    if (type.includes('mpeg') || type.includes('mp3')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('vorbis')) {
                        return 'probably';
                    }
                    if (type.includes('wav')) {
                        return 'probably';
                    }
                    if (type.includes('aac')) {
                        return 'probably';
                    }
                    return originalAudioCanPlayType.call(this, type);
                };

                // Advanced stealth measures for sophisticated detection
                // Hide automation traces and normalize browser behavior

                // Remove automation indicators
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                // CDP Detection Bypass - Block the specific CDP detection technique
                // This prevents detection of Chrome DevTools Protocol usage
                const originalConsoleLog = console.log;
                const originalConsoleError = console.error;
                const originalConsoleWarn = console.warn;
                const originalConsoleInfo = console.info;
                const originalConsoleDebug = console.debug;

                function bypassCDPDetection(originalMethod, methodName, ...args) {
                    // Log all console calls to help debug browser/platform detection
                    const timestamp = new Date().toISOString();
                    const argsStr = args.map(arg => {
                        if (typeof arg === 'object') {
                            try { return JSON.stringify(arg); }
                            catch { return '[Object]'; }
                        }
                        return String(arg);
                    }).join(', ');

                    // Send to Playwright console for debugging
                    if (window.playwrightDebug) {
                        window.playwrightDebug(`[${timestamp}] console.${methodName}(${argsStr})`);
                    }

                    // Only check if there's exactly one argument and it's an Error
                    if (args.length === 1 && args[0] instanceof Error) {
                        const error = args[0];
                        const descriptor = Object.getOwnPropertyDescriptor(error, 'stack');
                        if (descriptor && typeof descriptor.get === 'function') {
                            // This is likely CDP detection - create a safe version
                            const safeError = new Error(error.message);
                            safeError.name = error.name;
                            if (window.playwrightDebug) {
                                window.playwrightDebug(`[${timestamp}] CDP DETECTION BLOCKED: ${error.message}`);
                            }
                            // Call with safe error instead
                            return originalMethod.call(console, safeError);
                        }
                    }
                    // Normal console call - proceed as usual
                    return originalMethod.apply(console, args);
                }

                console.log = function(...args) { return bypassCDPDetection(originalConsoleLog, 'log', ...args); };
                console.error = function(...args) { return bypassCDPDetection(originalConsoleError, 'error', ...args); };
                console.warn = function(...args) { return bypassCDPDetection(originalConsoleWarn, 'warn', ...args); };
                console.info = function(...args) { return bypassCDPDetection(originalConsoleInfo, 'info', ...args); };
                console.debug = function(...args) { return bypassCDPDetection(originalConsoleDebug, 'debug', ...args); };
           ");


      // Set up route interception for HTML injection
      await page.RouteAsync( "**/*", async route =>
            {
              var request = route.Request;

              if (request.ResourceType == "document")
              {
                try
                {
                  var response = await route.FetchAsync();
                  var body = await response.TextAsync();

                  // Simple script injection
                  if (body.Contains("<head>"))
                  {
                    var injectedScript = @"
                                <script>
                                    // Fix H.264 codec detection via route injection
                                    const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                                    HTMLVideoElement.prototype.canPlayType = function(type) {
                                        if (type.includes('avc1') || type.includes('h264')) {
                                            return 'probably';
                                        }

                                        return originalCanPlayType.call(this, type);
                                    };

                                    // CDP Detection Bypass via route injection
                                    const origConsoleLog = console.log;
                                    const origConsoleError = console.error;
                                    const origConsoleWarn = console.warn;
                                    const origConsoleInfo = console.info;
                                    const origConsoleDebug = console.debug;

                                    function cdpBypass(origMethod, methodName, ...args) {
                                        // Log console calls for debugging
                                        const timestamp = new Date().toISOString();
                                        const argsStr = args.map(arg => {
                                            if (typeof arg === 'object') {
                                                try { return JSON.stringify(arg); }
                                                catch { return '[Object]'; }
                                            }
                                            return String(arg);
                                        }).join(', ');

                                        if (window.playwrightDebug) {
                                            window.playwrightDebug(`[ROUTE] [${timestamp}] console.${methodName}(${argsStr})`);
                                        }

                                        if (args.length === 1 && args[0] instanceof Error) {
                                            const error = args[0];
                                            const desc = Object.getOwnPropertyDescriptor(error, 'stack');
                                            if (desc && typeof desc.get === 'function') {
                                                const safeError = new Error(error.message);
                                                safeError.name = error.name;
                                                if (window.playwrightDebug) {
                                                    window.playwrightDebug(`[ROUTE] CDP DETECTION BLOCKED: ${error.message}`);
                                                }
                                                return origMethod.call(console, safeError);
                                            }
                                        }
                                        return origMethod.apply(console, args);
                                    }

                                    console.log = function(...args) { return cdpBypass(origConsoleLog, 'log', ...args); };
                                    console.error = function(...args) { return cdpBypass(origConsoleError, 'error', ...args); };
                                    console.warn = function(...args) { return cdpBypass(origConsoleWarn, 'warn', ...args); };
                                    console.info = function(...args) { return cdpBypass(origConsoleInfo, 'info', ...args); };
                                    console.debug = function(...args) { return cdpBypass(origConsoleDebug, 'debug', ...args); };

                                    delete Object.getPrototypeOf(navigator).webdriver
                                </script>";

                    body = body.Replace("<head>", $"<head>{injectedScript}");
                  }

                  await route.FulfillAsync(new RouteFulfillOptions
                  {
                    Status = response.Status,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value),
                    Body = body
                  });
                }
                catch
                {
                  // Fallback: continue without modification
                  await route.ContinueAsync();
                }
              }
              else
              {
                await route.ContinueAsync();
              }
            } );

      await page.SetViewportSizeAsync( 1920, 1080 );

      // Add page event listeners to debug what's happening
      page.DOMContentLoaded += (_, _) => Console.WriteLine("[PAGE EVENT] DOMContentLoaded fired");
      page.Load += (_, _) => Console.WriteLine("[PAGE EVENT] Load event fired");
      page.Request += (_, e) => Console.WriteLine($"[PAGE EVENT] Request: {e.Method} {e.Url}");
      page.Response += (_, e) => Console.WriteLine($"[PAGE EVENT] Response: {e.Status} {e.Url}");

      Console.WriteLine("Navigating to BrowserScan.net...");

      // await page.GotoAsync("https://beacon.schneidercorp.com/");
      // await WaitForExitKey();

      // await page.GotoAsync("https://bot.sannysoft.com/");
      // await WaitForExitKey();

      // await page.GotoAsync("https://fingerprint.com/products/bot-detection/");
      // await WaitForExitKey();

      // await page.GotoAsync("https://iphey.com/");
      // await WaitForExitKey();

      await page.GotoAsync( "https://browserscan.net/" );
      Console.WriteLine("Page loaded, waiting for tests to complete...");
      await WaitForExitKey();

      // // Take screenshot at the very end
      // Console.WriteLine( "Taking final screenshot..." );
      // await page.ScreenshotAsync( new PageScreenshotOptions { Path = "browserscan.net-test.png", FullPage = true } );
      // Console.WriteLine( "Screenshot saved!" );

      await page.CloseAsync();
      await browser.CloseAsync();

      Console.WriteLine( "Example completed successfully!" );
    }
    catch ( Exception ex )
    {
      Console.WriteLine( $"Error: {ex.Message}" );
      Console.WriteLine( $"Stack trace: {ex.StackTrace}" );
    }
  }



  public static async Task IdleAsync( int least = 0, int most = 0 )
  {
    // if they specified a delay, use that, otherwise random 1 to 3 seconds
    var wait = least > 0 && most == 0 ? least : least == 0 && most == 0 ? Random.Shared.Next(1000, 3000) : Random.Shared.Next(least, most);

    // let's keep track of when we started waiting...
    var start = DateTime.Now;

    // as long as we're short of our wait...
    while ( ( DateTime.Now - start ).TotalMilliseconds < wait )
    {
      // process other events
      await Task.Delay( 10 );
    }
  }

  public static async Task WaitForExitKey()
  {
    Console.WriteLine( "Press ESC or Ctrl to exit (works even when browser has focus)..." );
    Console.WriteLine( "Monitoring for hanging tests - will report status every 30 seconds..." );

    bool exitKeyPressed = false;
    var startTime = DateTime.Now;
    var lastStatusTime = DateTime.Now;

    while ( !exitKeyPressed )
    {
      // Check for escape key or control keys globally using Windows API
      // GetAsyncKeyState returns a value with the high bit set if the key is currently pressed
      bool escapePressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;
      bool leftCtrlPressed = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
      bool rightCtrlPressed = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

      if ( escapePressed || leftCtrlPressed || rightCtrlPressed )
      {
        exitKeyPressed = true;
        Console.WriteLine( "Exit key detected!" );
      }

      // Report status every 30 seconds to help identify hanging tests
      var elapsed = DateTime.Now - lastStatusTime;
      if ( elapsed.TotalSeconds >= 30 )
      {
        var totalElapsed = DateTime.Now - startTime;
        Console.WriteLine( $"[STATUS] Still running... Total elapsed: {totalElapsed.TotalMinutes:F1} minutes" );
        Console.WriteLine( $"[STATUS] If tests are hanging, check browser window for incomplete tests" );
        lastStatusTime = DateTime.Now;
      }

      await IdleAsync( 100, 200 );
    }
  }
}
