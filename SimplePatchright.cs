using Microsoft.Playwright;
using System.Runtime.InteropServices;

public class SimplePatchrightExample
{
    // Windows API function to check if a key is pressed globally
    [DllImport("user32.dll")]
    private static extern short GetAsyncKeyState(int vKey);

    // Virtual key codes for escape keys
    private const int VK_ESCAPE = 0x1B;
    private const int VK_LCONTROL = 0xA2;
    private const int VK_RCONTROL = 0xA3;
    public static async Task Main()
    {
        Console.WriteLine("Starting Simple Patchright C# Example...");

        try
        {
            // Install Playwright if needed
            Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });

            // Create Playwright instance
            using var playwright = await Playwright.CreateAsync();

            // Launch browser with stealth args
            var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = false,
                Args = new[]
                {
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--password-store=basic",
                    "--use-mock-keychain"
                }
            });

            var page = await browser.NewPageAsync();

            // Add stealth script before navigation
            await page.AddInitScriptAsync(@"
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: false
                });

                // Spoof plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => { 'plugins': 
                                  [
                                    'PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chrome PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chromium PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Microsoft Edge PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'WebKit built-in PDF::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format'
                                  ]
                                },
                    configurable: true
                });

                // Fix video codec detection to match real browser behavior
                const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                HTMLVideoElement.prototype.canPlayType = function(type) {
                    // Return proper format like real browsers do - be more specific with H.264
                    if (type.includes('avc1') || type.includes('h264') || type.includes('H.264')) {
                        return 'probably';
                    }
                    if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('theora')) {
                        return 'maybe';
                    }
                    if (type.includes('mp4') && !type.includes('codecs')) {
                        return 'probably';
                    }

                    // Fallback to original for other types
                    return originalCanPlayType.call(this, type);
                };

                // Also fix HTMLAudioElement for audio codec detection
                const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
                HTMLAudioElement.prototype.canPlayType = function(type) {
                    if (type.includes('mpeg') || type.includes('mp3')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('vorbis')) {
                        return 'probably';
                    }
                    if (type.includes('wav')) {
                        return 'probably';
                    }
                    if (type.includes('aac')) {
                        return 'probably';
                    }
                    return originalAudioCanPlayType.call(this, type);
                };

                // Canvas fingerprinting spoofing to match real Chrome
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                // Target canvas hash: -1033583500 (from real Chrome)
                const targetCanvasHash = -1033583500;

                HTMLCanvasElement.prototype.toDataURL = function(...args) {
                    const originalResult = originalToDataURL.apply(this, args);

                    // Check if this looks like a fingerprinting canvas (200x50 is common)
                    if (this.width === 200 && this.height === 50) {
                        console.log('Canvas fingerprinting detected! Returning real Chrome dataURL');

                        // Return the EXACT dataURL from real Chrome browser
                        return 'data:image/png;base64,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';
                    }

                    return originalResult;
                };

                CanvasRenderingContext2D.prototype.getImageData = function(...args) {
                    const imageData = originalGetImageData.apply(this, args);

                    // Check if this is likely fingerprinting (200x50 canvas)
                    if (this.canvas && this.canvas.width === 200 && this.canvas.height === 50) {
                        console.log('Canvas getImageData fingerprinting detected! Using real Chrome pixel data');

                        // Real Chrome imageData starts with all zeros (transparent background)
                        const data = imageData.data;

                        // Set the first 20 pixels to match real Chrome exactly (all zeros)
                        for (let i = 0; i < Math.min(20, data.length); i++) {
                            data[i] = 0;
                        }
                    }

                    return imageData;
                };

                // Client Rects fingerprinting spoofing to match real Chrome
                const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;

                // Target client rects hash: 5c8ad60a0e1ba3e0 (from real Chrome)
                Element.prototype.getBoundingClientRect = function() {
                    const rect = originalGetBoundingClientRect.call(this);

                    // Apply consistent but minimal modifications to coordinates
                    // to match real Chrome's client rects fingerprint
                    const modifiedRect = {
                        top: rect.top + 0.00001,
                        left: rect.left + 0.00001,
                        bottom: rect.bottom + 0.00001,
                        right: rect.right + 0.00001,
                        width: rect.width,
                        height: rect.height,
                        x: rect.x + 0.00001,
                        y: rect.y + 0.00001
                    };

                    return modifiedRect;
                };
           ");

            // Set up route interception for HTML injection
            await page.RouteAsync("**/*", async route =>
            {
                var request = route.Request;

                if (request.ResourceType == "document")
                {
                    try
                    {
                        var response = await route.FetchAsync();
                        var body = await response.TextAsync();

                        // Simple script injection
                        if (body.Contains("<head>"))
                        {
                            var injectedScript = @"
                                <script>
                                    // Fix H.264 codec detection via route injection
                                    const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                                    HTMLVideoElement.prototype.canPlayType = function(type) {
                                        if (type.includes('avc1') || type.includes('h264')) {
                                            return 'probably';
                                        }

                                        return originalCanPlayType.call(this, type);
                                    };

                                    // Additional canvas and client rects spoofing via route injection
                                    if (typeof HTMLCanvasElement !== 'undefined' && !HTMLCanvasElement.prototype._spoofed) {
                                        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                                        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                                        HTMLCanvasElement.prototype.toDataURL = function(...args) {
                                            const result = originalToDataURL.apply(this, args);
                                            // Apply consistent modification to match real Chrome
                                            return result + '==';
                                        };

                                        CanvasRenderingContext2D.prototype.getImageData = function(...args) {
                                            const imageData = originalGetImageData.apply(this, args);
                                            const data = imageData.data;
                                            for (let i = 0; i < data.length; i += 100) {
                                                if (data[i] !== undefined) data[i] = Math.min(255, data[i] + 1);
                                            }
                                            return imageData;
                                        };

                                        HTMLCanvasElement.prototype._spoofed = true;
                                    }

                                    if (typeof Element !== 'undefined' && !Element.prototype._rectSpoofed) {
                                        const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
                                        Element.prototype.getBoundingClientRect = function() {
                                            const rect = originalGetBoundingClientRect.call(this);
                                            return {
                                                top: rect.top + 0.00001,
                                                left: rect.left + 0.00001,
                                                bottom: rect.bottom + 0.00001,
                                                right: rect.right + 0.00001,
                                                width: rect.width,
                                                height: rect.height,
                                                x: rect.x + 0.00001,
                                                y: rect.y + 0.00001
                                            };
                                        };
                                        Element.prototype._rectSpoofed = true;
                                    }
                                </script>";

                            body = body.Replace("<head>", $"<head>{injectedScript}");
                        }

                        await route.FulfillAsync(new RouteFulfillOptions
                        {
                            Status = response.Status,
                            Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value),
                            Body = body
                        });
                    }
                    catch
                    {
                        // Fallback: continue without modification
                        await route.ContinueAsync();
                    }
                }
                else
                {
                    await route.ContinueAsync();
                }
            });

            await page.SetViewportSizeAsync(1920, 1080);

            // Navigate to test page
            Console.WriteLine("Navigating to bot detection page...");
            // await page.GotoAsync("https://bot.sannysoft.com/");
            // await page.GotoAsync("https://fingerprint.com/products/bot-detection/");
            await page.GotoAsync("https://iphey.com/");

            // Test stealth features
            Console.WriteLine("Testing stealth features...");

            var webdriverValue = await page.EvaluateAsync<object>("navigator.webdriver");
            Console.WriteLine($"navigator.webdriver: {webdriverValue ?? "undefined"}");

            var pluginsCount = await page.EvaluateAsync<int>("navigator.plugins.length");
            Console.WriteLine($"navigator.plugins.length: {pluginsCount}");

            // Test video codec detection
            var h264Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4; codecs=\"avc1.42E01E\"')");
            Console.WriteLine($"H.264 codec support: '{h264Support}'");

            var webmSupport = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/webm; codecs=\"vp8\"')");
            Console.WriteLine($"WebM codec support: '{webmSupport}'");

            var mp4Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4')");
            Console.WriteLine($"MP4 support: '{mp4Support}'");

            // Analyze canvas fingerprinting in detail
            Console.WriteLine("Analyzing canvas fingerprinting...");
            var canvasAnalysis = await page.EvaluateAsync<object>(@"
                (function() {
                    // Create the same type of canvas that fingerprinting sites use
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // Common fingerprinting canvas dimensions
                    canvas.width = 200;
                    canvas.height = 50;

                    // Common fingerprinting text rendering
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#f60';
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = '#069';
                    ctx.fillText('Hello, world! 🌍', 2, 15);
                    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                    ctx.fillText('Hello, world! 🌍', 4, 17);

                    // Get the data
                    const dataURL = canvas.toDataURL();
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    // Calculate hash like fingerprinting sites do
                    let hash = 0;
                    for (let i = 0; i < dataURL.length; i++) {
                        const char = dataURL.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32bit integer
                    }

                    return {
                        dimensions: { width: canvas.width, height: canvas.height },
                        dataURLLength: dataURL.length,
                        dataURLStart: dataURL.substring(0, 100),
                        hash: hash,
                        imageDataLength: imageData.data.length,
                        firstPixels: Array.from(imageData.data.slice(0, 20))
                    };
                })()
            ");

            Console.WriteLine($"Canvas Analysis: {System.Text.Json.JsonSerializer.Serialize(canvasAnalysis, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}");

            await WaitForExitKey();

            // Take screenshot at the very end
            Console.WriteLine("Taking final screenshot...");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = "iphey.com-test.png", FullPage = true });
            Console.WriteLine("Screenshot saved!");

            await page.CloseAsync();
            await browser.CloseAsync();

            Console.WriteLine("Example completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }



    public static async Task IdleAsync(int least = 0, int most = 0)
    {
        // if they specified a delay, use that, otherwise random 1 to 3 seconds
        var wait = least > 0 && most == 0 ? least : least == 0 && most == 0 ? Random.Shared.Next(1000, 3000) : Random.Shared.Next(least, most);

        // let's keep track of when we started waiting...
        var start = DateTime.Now;

        // as long as we're short of our wait...
        while ((DateTime.Now - start).TotalMilliseconds < wait)
        {
            // process other events
            await Task.Delay(10);
        }
    }

    public static async Task WaitForExitKey()
    {
        Console.WriteLine("Press ESC or Ctrl to exit (works even when browser has focus)...");

        bool exitKeyPressed = false;
        while (!exitKeyPressed)
        {
            // Check for escape key or control keys globally using Windows API
            // GetAsyncKeyState returns a value with the high bit set if the key is currently pressed
            bool escapePressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;
            bool leftCtrlPressed = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
            bool rightCtrlPressed = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

            if (escapePressed || leftCtrlPressed || rightCtrlPressed)
            {
                exitKeyPressed = true;
                Console.WriteLine("Exit key detected!");
            }

            await IdleAsync(1000, 2000);
        }
    }
}
