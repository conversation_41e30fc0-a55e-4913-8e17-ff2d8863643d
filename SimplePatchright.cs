using Microsoft.Playwright;
using System.Runtime.InteropServices;

public class SimplePatchrightExample
{
    // Windows API function to check if a key is pressed globally
    [DllImport("user32.dll")]
    private static extern short GetAsyncKeyState(int vKey);

    // Virtual key codes for escape keys
    private const int VK_ESCAPE = 0x1B;
    private const int VK_LCONTROL = 0xA2;
    private const int VK_RCONTROL = 0xA3;
    public static async Task Main()
    {
        Console.WriteLine("Starting Simple Patchright C# Example...");

        try
        {
            // Install Playwright if needed
            Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });

            // Create Playwright instance
            using var playwright = await Playwright.CreateAsync();

            // Launch browser with stealth args
            var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = false,
                Args = new[]
                {
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--password-store=basic",
                    "--use-mock-keychain"
                }
            });

            var page = await browser.NewPageAsync();

            // Add stealth script before navigation
            await page.AddInitScriptAsync(@"
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: false
                });

                // Spoof plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => { 'plugins': 
                                  [
                                    'PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chrome PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chromium PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Microsoft Edge PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'WebKit built-in PDF::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format'
                                  ]
                                },
                    configurable: true
                });

                // Fix video codec detection to match real browser behavior
                const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                HTMLVideoElement.prototype.canPlayType = function(type) {
                    // Return proper format like real browsers do - be more specific with H.264
                    if (type.includes('avc1') || type.includes('h264') || type.includes('H.264')) {
                        return 'probably';
                    }
                    if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('theora')) {
                        return 'maybe';
                    }
                    if (type.includes('mp4') && !type.includes('codecs')) {
                        return 'probably';
                    }

                    // Fallback to original for other types
                    return originalCanPlayType.call(this, type);
                };

                // Also fix HTMLAudioElement for audio codec detection
                const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
                HTMLAudioElement.prototype.canPlayType = function(type) {
                    if (type.includes('mpeg') || type.includes('mp3')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('vorbis')) {
                        return 'probably';
                    }
                    if (type.includes('wav')) {
                        return 'probably';
                    }
                    if (type.includes('aac')) {
                        return 'probably';
                    }
                    return originalAudioCanPlayType.call(this, type);
                };

                // Canvas fingerprinting spoofing to match real Chrome
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                // Target canvas hash: -1033583500 (from real Chrome)
                const targetCanvasHash = -1033583500;

                HTMLCanvasElement.prototype.toDataURL = function(...args) {
                    const originalResult = originalToDataURL.apply(this, args);

                    // Simple hash function to check if we need to modify
                    let hash = 0;
                    for (let i = 0; i < originalResult.length; i++) {
                        const char = originalResult.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32bit integer
                    }

                    // If hash doesn't match target, apply minimal modification
                    if (hash !== targetCanvasHash) {
                        // Modify the data URL slightly to achieve target hash
                        // This is a simplified approach - in practice you'd need more sophisticated methods
                        return originalResult + '=='; // Add padding to change hash
                    }

                    return originalResult;
                };

                CanvasRenderingContext2D.prototype.getImageData = function(...args) {
                    const imageData = originalGetImageData.apply(this, args);

                    // Apply minimal noise to match real Chrome canvas fingerprint
                    const data = imageData.data;
                    for (let i = 0; i < data.length; i += 4) {
                        // Apply very subtle modifications to RGB values
                        if (i % 100 === 0) { // Only modify every 25th pixel
                            data[i] = Math.min(255, data[i] + 1);     // R
                            data[i + 1] = Math.min(255, data[i + 1] + 1); // G
                            data[i + 2] = Math.min(255, data[i + 2] + 1); // B
                        }
                    }

                    return imageData;
                };

                // Client Rects fingerprinting spoofing to match real Chrome
                const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;

                // Target client rects hash: 5c8ad60a0e1ba3e0 (from real Chrome)
                Element.prototype.getBoundingClientRect = function() {
                    const rect = originalGetBoundingClientRect.call(this);

                    // Apply consistent but minimal modifications to coordinates
                    // to match real Chrome's client rects fingerprint
                    const modifiedRect = {
                        top: rect.top + 0.00001,
                        left: rect.left + 0.00001,
                        bottom: rect.bottom + 0.00001,
                        right: rect.right + 0.00001,
                        width: rect.width,
                        height: rect.height,
                        x: rect.x + 0.00001,
                        y: rect.y + 0.00001
                    };

                    return modifiedRect;
                };
           ");

            // Set up route interception for HTML injection
            await page.RouteAsync("**/*", async route =>
            {
                var request = route.Request;

                if (request.ResourceType == "document")
                {
                    try
                    {
                        var response = await route.FetchAsync();
                        var body = await response.TextAsync();

                        // Simple script injection
                        if (body.Contains("<head>"))
                        {
                            var injectedScript = @"
                                <script>
                                    // Fix H.264 codec detection via route injection
                                    const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                                    HTMLVideoElement.prototype.canPlayType = function(type) {
                                        if (type.includes('avc1') || type.includes('h264')) {
                                            return 'probably';
                                        }

                                        return originalCanPlayType.call(this, type);
                                    };

                                    // Additional canvas and client rects spoofing via route injection
                                    if (typeof HTMLCanvasElement !== 'undefined' && !HTMLCanvasElement.prototype._spoofed) {
                                        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                                        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                                        HTMLCanvasElement.prototype.toDataURL = function(...args) {
                                            const result = originalToDataURL.apply(this, args);
                                            // Apply consistent modification to match real Chrome
                                            return result + '==';
                                        };

                                        CanvasRenderingContext2D.prototype.getImageData = function(...args) {
                                            const imageData = originalGetImageData.apply(this, args);
                                            const data = imageData.data;
                                            for (let i = 0; i < data.length; i += 100) {
                                                if (data[i] !== undefined) data[i] = Math.min(255, data[i] + 1);
                                            }
                                            return imageData;
                                        };

                                        HTMLCanvasElement.prototype._spoofed = true;
                                    }

                                    if (typeof Element !== 'undefined' && !Element.prototype._rectSpoofed) {
                                        const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
                                        Element.prototype.getBoundingClientRect = function() {
                                            const rect = originalGetBoundingClientRect.call(this);
                                            return {
                                                top: rect.top + 0.00001,
                                                left: rect.left + 0.00001,
                                                bottom: rect.bottom + 0.00001,
                                                right: rect.right + 0.00001,
                                                width: rect.width,
                                                height: rect.height,
                                                x: rect.x + 0.00001,
                                                y: rect.y + 0.00001
                                            };
                                        };
                                        Element.prototype._rectSpoofed = true;
                                    }
                                </script>";

                            body = body.Replace("<head>", $"<head>{injectedScript}");
                        }

                        await route.FulfillAsync(new RouteFulfillOptions
                        {
                            Status = response.Status,
                            Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value),
                            Body = body
                        });
                    }
                    catch
                    {
                        // Fallback: continue without modification
                        await route.ContinueAsync();
                    }
                }
                else
                {
                    await route.ContinueAsync();
                }
            });

            await page.SetViewportSizeAsync(1920, 1080);

            // Navigate to test page
            Console.WriteLine("Navigating to bot detection page...");
            // await page.GotoAsync("https://bot.sannysoft.com/");
            // await page.GotoAsync("https://fingerprint.com/products/bot-detection/");
            await page.GotoAsync("https://iphey.com/");

            // Test stealth features
            Console.WriteLine("Testing stealth features...");

            var webdriverValue = await page.EvaluateAsync<object>("navigator.webdriver");
            Console.WriteLine($"navigator.webdriver: {webdriverValue ?? "undefined"}");

            var pluginsCount = await page.EvaluateAsync<int>("navigator.plugins.length");
            Console.WriteLine($"navigator.plugins.length: {pluginsCount}");

            // Test video codec detection
            var h264Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4; codecs=\"avc1.42E01E\"')");
            Console.WriteLine($"H.264 codec support: '{h264Support}'");

            var webmSupport = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/webm; codecs=\"vp8\"')");
            Console.WriteLine($"WebM codec support: '{webmSupport}'");

            var mp4Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4')");
            Console.WriteLine($"MP4 support: '{mp4Support}'");

            Console.WriteLine("Press ESC or Ctrl to exit (works even when browser has focus)...");

            bool exitKeyPressed = false;
            while (!exitKeyPressed)
            {
                // Check for escape key or control keys globally using Windows API
                // GetAsyncKeyState returns a value with the high bit set if the key is currently pressed
                bool escapePressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;
                bool leftCtrlPressed = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
                bool rightCtrlPressed = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

                if (escapePressed || leftCtrlPressed || rightCtrlPressed)
                {
                    exitKeyPressed = true;
                    Console.WriteLine("Exit key detected!");
                }

                await IdleAsync(1000, 2000);
            }

            // Take screenshot
            Console.WriteLine("Taking screenshot...");
            // await page.ScreenshotAsync(new PageScreenshotOptions { Path = "sannysoft-test.png", FullPage = true });
            // await page.ScreenshotAsync(new PageScreenshotOptions { Path = "fingerprint.com-test.png", FullPage = true });
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = "iphey.com-test.png", FullPage = true });
            Console.WriteLine("Screenshot saved!");
             await page.CloseAsync();
            await browser.CloseAsync();

            Console.WriteLine("Example completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }



    public static async Task IdleAsync(int least = 0, int most = 0)
    {
        // if they specified a delay, use that, otherwise random 1 to 3 seconds
        var wait = least > 0 && most == 0 ? least : least == 0 && most == 0 ? Random.Shared.Next(1000, 3000) : Random.Shared.Next(least, most);

        // let's keep track of when we started waiting...
        var start = DateTime.Now;

        // as long as we're short of our wait... 
        while ((DateTime.Now - start).TotalMilliseconds < wait)
        {
            // process other events
            await Task.Delay(10);
        }
    }
}
