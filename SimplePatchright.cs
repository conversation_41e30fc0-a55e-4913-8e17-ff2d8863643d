using Microsoft.Playwright;
using System.Runtime.InteropServices;

public class SimplePatchrightExample
{
  // Windows API function to check if a key is pressed globally
  [DllImport( "user32.dll" )]
  private static extern short GetAsyncKeyState( int vKey );

  // Virtual key codes for escape keys
  private const int VK_ESCAPE = 0x1B;
  private const int VK_LCONTROL = 0xA2;
  private const int VK_RCONTROL = 0xA3;
  public static async Task Main()
  {
    Console.WriteLine( "Starting Simple Patchright C# Example..." );

    try
    {
      // Install Playwright if needed
      Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });
      Microsoft.Playwright.Program.Main(new[] { "install", "firefox" });

      // Create Playwright instance
      using var playwright = await Playwright.CreateAsync();

      // Choose browser type - set this to switch between browsers
      bool useFirefox = false; // Set to true for Firefox

      IBrowser browser;
      IPage page;

      if (useFirefox)
      {
        // Launch Firefox with stealth configuration
        browser = await playwright.Firefox.LaunchAsync( new BrowserTypeLaunchOptions
        {
          Headless = false,
          // Firefox-specific arguments for stealth
          Args = new[]
          {
            "--no-first-run",
            "--disable-default-browser-check",
            "--disable-dev-shm-usage"
          }
        });

        page = await browser.NewPageAsync(new BrowserNewPageOptions
        {
          UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0"
        });

        // Apply Firefox-specific stealth measures
        await ApplyFirefoxStealth(page);
      }
      else
      {
        // Launch Chromium with stealth configuration
        browser = await playwright.Chromium.LaunchAsync( new BrowserTypeLaunchOptions
        {
          Headless = false,
          Args = new[]
          {
              "--disable-blink-features=AutomationControlled",
              "--no-first-run",
              "--no-service-autorun",
              "--no-default-browser-check",
              "--disable-background-timer-throttling",
              "--disable-backgrounding-occluded-windows",
              "--disable-renderer-backgrounding",
              "--disable-features=TranslateUI",
              "--disable-component-extensions-with-background-pages"
          }
        });

        page = await browser.NewPageAsync(new BrowserNewPageOptions
        {
          UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.114 Safari/537.36"
        });

        // Keep the existing working Chromium stealth code
        // Add stealth script before navigation
        await page.AddInitScriptAsync(@"
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                    configurable: false
                });

                Object.defineProperty(window, 'outerWidth', {
                    get: () => window.innerWidth
                });

                // Spoof plugins - Add fake plugins to the real PluginArray
                (function() {
                    try {
                        // Get the original plugins array
                        const originalPlugins = navigator.plugins;

                        // Try to add fake plugins to the existing array
                        const fakePluginData = [
                            { name: 'PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                            { name: 'Chrome PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                            { name: 'Chromium PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                            { name: 'Microsoft Edge PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                            { name: 'WebKit built-in PDF', description: 'Portable Document Format', filename: 'internal-pdf-viewer' }
                        ];

                        // Add fake plugins to the original array
                        fakePluginData.forEach((plugin, index) => {
                            try {
                                originalPlugins[originalPlugins.length + index] = plugin;
                            } catch (e) {
                                // If we can't modify the original, fall back to replacement
                                console.log('Cannot modify original plugins, using fallback');
                            }
                        });

                        // Try to update the length property
                        try {
                            Object.defineProperty(originalPlugins, 'length', {
                                value: originalPlugins.length + fakePluginData.length,
                                configurable: true
                            });
                        } catch (e) {
                            // Length might be read-only, that's okay
                        }

                    } catch (e) {
                        console.log('Plugin spoofing failed:', e);
                    }
                })();

                // Fix video codec detection to match real browser behavior
                const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                HTMLVideoElement.prototype.canPlayType = function(type) {
                    // Return proper format like real browsers do - be more specific with H.264
                    if (type.includes('avc1') || type.includes('h264') || type.includes('H.264')) {
                        return 'probably';
                    }
                    if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('theora')) {
                        return 'maybe';
                    }
                    if (type.includes('mp4') && !type.includes('codecs')) {
                        return 'probably';
                    }

                    // Fallback to original for other types
                    return originalCanPlayType.call(this, type);
                };

                // Also fix HTMLAudioElement for audio codec detection
                const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
                HTMLAudioElement.prototype.canPlayType = function(type) {
                    if (type.includes('mpeg') || type.includes('mp3')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('vorbis')) {
                        return 'probably';
                    }
                    if (type.includes('wav')) {
                        return 'probably';
                    }
                    if (type.includes('aac')) {
                        return 'probably';
                    }
                    return originalAudioCanPlayType.call(this, type);
                };

                // Advanced stealth measures for sophisticated detection
                // Hide automation traces and normalize browser behavior

                // Remove automation indicators
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                // CDP Detection Bypass - Block the specific CDP detection technique
                // This prevents detection of Chrome DevTools Protocol usage
                const originalConsoleLog = console.log;
                const originalConsoleError = console.error;
                const originalConsoleWarn = console.warn;
                const originalConsoleInfo = console.info;
                const originalConsoleDebug = console.debug;

                function bypassCDPDetection(originalMethod, methodName, ...args) {
                    // Only intercept very specific CDP detection patterns
                    // Let most console calls through normally to avoid breaking legitimate code

                    // Only check if there's exactly one argument and it's an Error with custom stack getter
                    if (args.length === 1 && args[0] instanceof Error) {
                        const error = args[0];
                        const descriptor = Object.getOwnPropertyDescriptor(error, 'stack');
                        if (descriptor && typeof descriptor.get === 'function') {
                            // This is the specific CDP detection pattern - block it
                            const safeError = new Error(error.message);
                            safeError.name = error.name;
                            return originalMethod.call(console, safeError);
                        }
                    }

                    // All other console calls pass through normally - no logging to avoid interference
                    return originalMethod.apply(console, args);
                }

                console.log = function(...args) { return bypassCDPDetection(originalConsoleLog, 'log', ...args); };
                console.error = function(...args) { return bypassCDPDetection(originalConsoleError, 'error', ...args); };
                console.warn = function(...args) { return bypassCDPDetection(originalConsoleWarn, 'warn', ...args); };
                console.info = function(...args) { return bypassCDPDetection(originalConsoleInfo, 'info', ...args); };
                console.debug = function(...args) { return bypassCDPDetection(originalConsoleDebug, 'debug', ...args); };
           ");
      }


      // Set up route interception for HTML injection
      await page.RouteAsync( "**/*", async route =>
            {
              var request = route.Request;

              if (request.ResourceType == "document")
              {
                try
                {
                  var response = await route.FetchAsync();
                  var body = await response.TextAsync();

                  // Simple script injection
                  if (body.Contains("<head>"))
                  {
                    var injectedScript = @"
                                <script>
                                    // Fix H.264 codec detection via route injection
                                    const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                                    HTMLVideoElement.prototype.canPlayType = function(type) {
                                        if (type.includes('avc1') || type.includes('h264')) {
                                            return 'probably';
                                        }

                                        return originalCanPlayType.call(this, type);
                                    };

                                    // CDP Detection Bypass via route injection
                                    const origConsoleLog = console.log;
                                    const origConsoleError = console.error;
                                    const origConsoleWarn = console.warn;
                                    const origConsoleInfo = console.info;
                                    const origConsoleDebug = console.debug;

                                    function cdpBypass(origMethod, methodName, ...args) {
                                        // Only block specific CDP detection patterns
                                        if (args.length === 1 && args[0] instanceof Error) {
                                            const error = args[0];
                                            const desc = Object.getOwnPropertyDescriptor(error, 'stack');
                                            if (desc && typeof desc.get === 'function') {
                                                const safeError = new Error(error.message);
                                                safeError.name = error.name;
                                                return origMethod.call(console, safeError);
                                            }
                                        }
                                        // All other console calls pass through normally
                                        return origMethod.apply(console, args);
                                    }

                                    console.log = function(...args) { return cdpBypass(origConsoleLog, 'log', ...args); };
                                    console.error = function(...args) { return cdpBypass(origConsoleError, 'error', ...args); };
                                    console.warn = function(...args) { return cdpBypass(origConsoleWarn, 'warn', ...args); };
                                    console.info = function(...args) { return cdpBypass(origConsoleInfo, 'info', ...args); };
                                    console.debug = function(...args) { return cdpBypass(origConsoleDebug, 'debug', ...args); };

                                    delete Object.getPrototypeOf(navigator).webdriver
                                </script>";

                    body = body.Replace("<head>", $"<head>{injectedScript}");
                  }

                  await route.FulfillAsync(new RouteFulfillOptions
                  {
                    Status = response.Status,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value),
                    Body = body
                  });
                }
                catch
                {
                  // Fallback: continue without modification
                  await route.ContinueAsync();
                }
              }
              else
              {
                await route.ContinueAsync();
              }
            } );

      await page.SetViewportSizeAsync( 1920, 1080 );

      // Comment out other tests to focus on sannysoft
      // await page.GotoAsync( "https://beacon.schneidercorp.com/" );
      // await WaitForExitKey();

      await page.GotoAsync( "https://bot.sannysoft.com/" );
      await WaitForExitKey();

      // await page.GotoAsync( "https://fingerprint.com/products/bot-detection/" );
      // await WaitForExitKey();

      // await page.GotoAsync( "https://iphey.com/" );
      // await WaitForExitKey();

      // await page.GotoAsync( "https://browserscan.net/" );
      // await WaitForExitKey();

      await page.CloseAsync();
      await browser.CloseAsync();

      Console.WriteLine( "Example completed successfully!" );
    }
    catch ( Exception ex )
    {
      Console.WriteLine( $"Error: {ex.Message}" );
      Console.WriteLine( $"Stack trace: {ex.StackTrace}" );
    }
  }



  public static async Task IdleAsync( int least = 0, int most = 0 )
  {
    // if they specified a delay, use that, otherwise random 1 to 3 seconds
    var wait = least > 0 && most == 0 ? least : least == 0 && most == 0 ? Random.Shared.Next(1000, 3000) : Random.Shared.Next(least, most);

    // let's keep track of when we started waiting...
    var start = DateTime.Now;

    // as long as we're short of our wait...
    while ( ( DateTime.Now - start ).TotalMilliseconds < wait )
    {
      // process other events
      await Task.Delay( 10 );
    }
  }

  public static async Task WaitForExitKey()
  {
    Console.WriteLine( "Press ESC to exit (works even when browser has focus)..." );

    bool exitKeyPressed = false;
    while ( !exitKeyPressed )
    {
      // Check for escape key globally using Windows API
      // GetAsyncKeyState returns a value with the high bit set if the key is currently pressed
      bool escapePressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;

      if ( escapePressed )
      {
        exitKeyPressed = true;
        Console.WriteLine( "Exit key detected!" );
      }

      await IdleAsync( 100, 200 );
    }
  }

  // Firefox-specific stealth measures
  public static async Task ApplyFirefoxStealth(IPage page)
  {
    await page.AddInitScriptAsync(@"
      // Hide webdriver property
      Object.defineProperty(navigator, 'webdriver', {
          get: () => false,
          configurable: false
      });

      // Firefox-specific automation indicators to remove
      delete window.fxdriver_id;
      delete window.fxdriver_unwrapped;
      delete window.webdriver_unwrapped;
      delete window.webdriver_script_fn;

      // Firefox-specific plugin spoofing - simple working approach
      Object.defineProperty(navigator, 'plugins', {
          get: function() {
              return {
                  length: 5,
                  0: { name: 'PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                  1: { name: 'Chrome PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                  2: { name: 'Chromium PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                  3: { name: 'Microsoft Edge PDF Viewer', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                  4: { name: 'WebKit built-in PDF', description: 'Portable Document Format', filename: 'internal-pdf-viewer' },
                  item: function(index) { return this[index] || null; },
                  namedItem: function(name) {
                      for (let i = 0; i < this.length; i++) {
                          if (this[i] && this[i].name === name) return this[i];
                      }
                      return null;
                  },
                  refresh: function() {}
              };
          },
          configurable: true
      });

      // Firefox-specific codec support
      const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
      HTMLVideoElement.prototype.canPlayType = function(type) {
          if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
              return 'probably';
          }
          if (type.includes('ogg') || type.includes('theora')) {
              return 'probably';
          }
          if (type.includes('mp4') || type.includes('h264') || type.includes('avc1')) {
              return 'maybe';
          }
          return originalCanPlayType.call(this, type);
      };

      // Firefox audio codec support
      const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
      HTMLAudioElement.prototype.canPlayType = function(type) {
          if (type.includes('ogg') || type.includes('vorbis')) {
              return 'probably';
          }
          if (type.includes('wav')) {
              return 'probably';
          }
          if (type.includes('mp3') || type.includes('mpeg')) {
              return 'maybe';
          }
          if (type.includes('aac')) {
              return 'maybe';
          }
          return originalAudioCanPlayType.call(this, type);
      };

      // Firefox-specific window properties
      Object.defineProperty(window, 'outerWidth', {
          get: () => window.innerWidth
      });
      Object.defineProperty(window, 'outerHeight', {
          get: () => window.innerHeight
      });
    ");
  }
}
