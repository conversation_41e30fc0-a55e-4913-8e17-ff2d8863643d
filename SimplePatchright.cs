using Microsoft.Playwright;
using System.Runtime.InteropServices;

public class SimplePatchrightExample
{
    // Windows API function to check if a key is pressed globally
    [DllImport("user32.dll")]
    private static extern short GetAsyncKeyState(int vKey);

    // Virtual key codes for escape keys
    private const int VK_ESCAPE = 0x1B;
    private const int VK_LCONTROL = 0xA2;
    private const int VK_RCONTROL = 0xA3;
    public static async Task Main()
    {
        Console.WriteLine("Starting Simple Patchright C# Example...");

        try
        {
            // Install Playwright if needed
            Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });

            // Create Playwright instance
            using var playwright = await Playwright.CreateAsync();

            // Launch browser with stealth args
            var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = false,
                Args = new[]
                {
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--password-store=basic",
                    "--use-mock-keychain"
                }
            });

            var page = await browser.NewPageAsync();

            // Add stealth script before navigation
            await page.AddInitScriptAsync(@"
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: false
                });

                // Spoof plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => { 'plugins': 
                                  [
                                    'PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chrome PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Chromium PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'Microsoft Edge PDF Viewer::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format',
                                    'WebKit built-in PDF::Portable Document Format::internal-pdf-viewer::__application/pdf~pdf~Portable Document Format,text/pdf~pdf~Portable Document Format'
                                  ]
                                },
                    configurable: true
                });

                // Fix video codec detection to match real browser behavior
                const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                HTMLVideoElement.prototype.canPlayType = function(type) {
                    // Return proper format like real browsers do - be more specific with H.264
                    if (type.includes('avc1') || type.includes('h264') || type.includes('H.264')) {
                        return 'probably';
                    }
                    if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('theora')) {
                        return 'maybe';
                    }
                    if (type.includes('mp4') && !type.includes('codecs')) {
                        return 'probably';
                    }

                    // Fallback to original for other types
                    return originalCanPlayType.call(this, type);
                };

                // Also fix HTMLAudioElement for audio codec detection
                const originalAudioCanPlayType = HTMLAudioElement.prototype.canPlayType;
                HTMLAudioElement.prototype.canPlayType = function(type) {
                    if (type.includes('mpeg') || type.includes('mp3')) {
                        return 'probably';
                    }
                    if (type.includes('ogg') || type.includes('vorbis')) {
                        return 'probably';
                    }
                    if (type.includes('wav')) {
                        return 'probably';
                    }
                    if (type.includes('aac')) {
                        return 'probably';
                    }
                    return originalAudioCanPlayType.call(this, type);
                }; 
           ");

            // Set up route interception for HTML injection
            await page.RouteAsync("**/*", async route =>
            {
                var request = route.Request;

                if (request.ResourceType == "document")
                {
                    try
                    {
                        var response = await route.FetchAsync();
                        var body = await response.TextAsync();

                        // Simple script injection
                        if (body.Contains("<head>"))
                        {
                            var injectedScript = @"
                                <script>
                                    // Fix H.264 codec detection via route injection
                                    const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
                                    HTMLVideoElement.prototype.canPlayType = function(type) {
                                        if (type.includes('avc1') || type.includes('h264')) {
                                            return 'probably';
                                        }

                                        return originalCanPlayType.call(this, type);
                                    };
                                </script>";

                            body = body.Replace("<head>", $"<head>{injectedScript}");
                        }

                        await route.FulfillAsync(new RouteFulfillOptions
                        {
                            Status = response.Status,
                            Headers = response.Headers.ToDictionary(h => h.Key, h => h.Value),
                            Body = body
                        });
                    }
                    catch
                    {
                        // Fallback: continue without modification
                        await route.ContinueAsync();
                    }
                }
                else
                {
                    await route.ContinueAsync();
                }
            });

            await page.SetViewportSizeAsync(1920, 1080);

            // Navigate to test page
            Console.WriteLine("Navigating to bot detection page...");
            // await page.GotoAsync("https://bot.sannysoft.com/");
            // await page.GotoAsync("https://fingerprint.com/products/bot-detection/");
            await page.GotoAsync("https://iphey.com/");

            // Test stealth features
            Console.WriteLine("Testing stealth features...");

            var webdriverValue = await page.EvaluateAsync<object>("navigator.webdriver");
            Console.WriteLine($"navigator.webdriver: {webdriverValue ?? "undefined"}");

            var pluginsCount = await page.EvaluateAsync<int>("navigator.plugins.length");
            Console.WriteLine($"navigator.plugins.length: {pluginsCount}");

            // Test video codec detection
            var h264Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4; codecs=\"avc1.42E01E\"')");
            Console.WriteLine($"H.264 codec support: '{h264Support}'");

            var webmSupport = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/webm; codecs=\"vp8\"')");
            Console.WriteLine($"WebM codec support: '{webmSupport}'");

            var mp4Support = await page.EvaluateAsync<string>("document.createElement('video').canPlayType('video/mp4')");
            Console.WriteLine($"MP4 support: '{mp4Support}'");

            // Take screenshot
            Console.WriteLine("Taking screenshot...");
            // await page.ScreenshotAsync(new PageScreenshotOptions { Path = "sannysoft-test.png", FullPage = true });
            // await page.ScreenshotAsync(new PageScreenshotOptions { Path = "fingerprint.com-test.png", FullPage = true });
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = "iphey.com-test.png", FullPage = true });
            Console.WriteLine("Screenshot saved!");

            Console.WriteLine("Press ESC or Ctrl to exit (works even when browser has focus)...");

            bool exitKeyPressed = false;
            while (!exitKeyPressed)
            {
                // Check for escape key or control keys globally using Windows API
                // GetAsyncKeyState returns a value with the high bit set if the key is currently pressed
                bool escapePressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;
                bool leftCtrlPressed = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
                bool rightCtrlPressed = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

                if (escapePressed || leftCtrlPressed || rightCtrlPressed)
                {
                    exitKeyPressed = true;
                    Console.WriteLine("Exit key detected!");
                }

                await IdleAsync(1000, 2000);
            }

            await page.CloseAsync();
            await browser.CloseAsync();

            Console.WriteLine("Example completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }



    public static async Task IdleAsync(int least = 0, int most = 0)
    {
        // if they specified a delay, use that, otherwise random 1 to 3 seconds
        var wait = least > 0 && most == 0 ? least : least == 0 && most == 0 ? Random.Shared.Next(1000, 3000) : Random.Shared.Next(least, most);

        // let's keep track of when we started waiting...
        var start = DateTime.Now;

        // as long as we're short of our wait... 
        while ((DateTime.Now - start).TotalMilliseconds < wait)
        {
            // process other events
            await Task.Delay(10);
        }
    }
}
