# Patchright C# Implementation

A C# implementation of Patchright functionality using Playwright routes for network interception and script injection.

## Prerequisites

- .NET 8.0 or later
- PowerShell (for Playwright browser installation)

## Quick Start

### Option 1: Automated Setup (Recommended)

1. **Run the setup script:**
   ```powershell
   .\setup.ps1
   ```

2. **Run the example:**
   ```bash
   dotnet run
   ```

### Option 2: Manual Setup

1. **Restore packages:**
   ```bash
   dotnet restore
   ```

2. **Build the project:**
   ```bash
   dotnet build
   ```

3. **Install Playwright browsers:**
   ```bash
   pwsh bin/Debug/net8.0/playwright.ps1 install chromium
   ```

4. **Run the example:**
   ```bash
   dotnet run
   ```

## What the Example Does

The example demonstrates:

1. **Stealth Features:**
   - Hides `navigator.webdriver` property
   - Spoofs `navigator.plugins`
   - Overrides permissions API

2. **Network Interception:**
   - Intercepts HTML responses
   - Injects scripts into pages
   - Handles CSP nonces safely

3. **Script Injection:**
   - Adds init scripts to every page
   - Exposes custom bindings
   - Cleans up after injection

## Expected Output

```
Starting Patchright C# Example...
Page created successfully!
Navigating to example.com...
Taking screenshot...
Screenshot saved as patchright-test.png
Testing stealth features...
navigator.webdriver: undefined
navigator.plugins.length: 2
User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...
Example completed successfully!
```

## Key Advantages Over Original Patchright

1. **Cleaner Architecture:** Uses Playwright's built-in route system
2. **Safer Implementation:** Configurable security features
3. **Better Error Handling:** Graceful fallbacks
4. **More Maintainable:** No complex AST manipulation
5. **Version Independent:** Works with any Playwright version

## Configuration Options

```csharp
var config = new PatchrightConfig
{
    EnableCspBypass = true,        // Allow CSP header modification
    EnableScriptInjection = true, // Enable script injection
    EnableNetworkInterception = true // Enable network interception
};
```

## Troubleshooting

### "Playwright browsers not found"
Run the browser installation:
```bash
pwsh bin/Debug/net8.0/playwright.ps1 install chromium
```

### "Permission denied" on PowerShell script
Enable script execution:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Build errors
Ensure you have .NET 8.0 installed:
```bash
dotnet --version
```

## Files Overview

- `PatchrightCSharpExample.cs` - Main implementation
- `PatchrightUsageExample.cs` - Usage examples
- `Program.cs` - Entry point
- `Patchright.CSharp.csproj` - Project file
- `setup.ps1` - Automated setup script

## Next Steps

1. Test with different websites
2. Add more stealth techniques
3. Implement closed shadow root support
4. Add performance optimizations
5. Create NuGet package

## Security Note

This implementation includes CSP bypass capabilities. Use responsibly and only for legitimate testing purposes.
