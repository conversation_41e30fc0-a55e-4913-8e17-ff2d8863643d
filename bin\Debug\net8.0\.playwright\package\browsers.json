{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1105", "installByDefault": true, "browserVersion": "123.0.6312.4"}, {"name": "chromium-with-symbols", "revision": "1105", "installByDefault": false, "browserVersion": "123.0.6312.4"}, {"name": "chromium-tip-of-tree", "revision": "1195", "installByDefault": false, "browserVersion": "123.0.6312.0"}, {"name": "firefox", "revision": "1440", "installByDefault": true, "browserVersion": "123.0"}, {"name": "firefox-asan", "revision": "1440", "installByDefault": false, "browserVersion": "123.0"}, {"name": "firefox-beta", "revision": "1440", "installByDefault": false, "browserVersion": "124.0b3"}, {"name": "webkit", "revision": "1983", "installByDefault": true, "revisionOverrides": {"mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816"}, "browserVersion": "17.4"}, {"name": "ffmpeg", "revision": "1009", "installByDefault": true}, {"name": "android", "revision": "1000", "installByDefault": false}]}