{"version": "0.2.0", "configurations": [{"name": "Launch Patchright C#", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/Patchright.CSharp.dll", "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart"}, {"name": "Attach to Process", "type": "coreclr", "request": "attach"}]}